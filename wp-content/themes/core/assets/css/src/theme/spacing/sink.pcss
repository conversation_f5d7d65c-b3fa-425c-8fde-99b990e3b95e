/* -------------------------------------------------------------------------
 *
 * Vertical Spacing: "Kitchen Sink"
 *
 * Generally Applicable to WYSIWYG containers, the kitchen sink applies
 * vertical spacing to all typography & media elements at once.
 *
 * This class should be used as sparingly as possible.
 *
 * ------------------------------------------------------------------------- */

/* -----------------------------------------------------------------------------
 * Kitchen Sink Vertical Spacing
 *
 * Applicable to the front-end theme only.
 * ----------------------------------------------------------------------------- */

.s-sink >,
.s-sink > .wp-block-group >,
.s-sink > .wp-block-group > .wp-block-group__inner-container >,
.s-sink > .block-editor-inner-blocks > .block-editor-block-list__layout > {
	/* -----------------------------------------------------------------------------
	 * First & Last Element Overrides
	 * ----------------------------------------------------------------------------- */

	/* CASE: remove top margin from first element */
	*:first-child {
		margin-top: 0;
	}

	/* CASE: remove bottom margin from bottom element */
	*:last-child {
		margin-bottom: 0;
	}

	/* -----------------------------------------------------------------------------
	 * Images
	 * ----------------------------------------------------------------------------- */

	.wp-image,
	.wp-block-image {
		margin-top: var(--spacer-60);
		margin-bottom: var(--spacer-60);

		.alignleft,
		.alignright {
			margin-top: var(--spacer-40);
			margin-bottom: var(--spacer-40);

			@media (--viewport-medium) {
				margin-top: 5px;
			}
		}
	}

	/* -----------------------------------------------------------------------------
	 * Audio, Videos, Embeds & File: Alignments
	 * ----------------------------------------------------------------------------- */

	.wp-block-audio,
	.wp-block-video,
	.wp-block-embed,
	.wp-block-file {
		margin-top: var(--spacer-60);
		margin-bottom: var(--spacer-60);
	}

	/* -----------------------------------------------------------------------------
	 * Overline
	 *
	 * Overrides default selector spacing.
	 * ----------------------------------------------------------------------------- */

	.is-style-t-overline {
		margin-bottom: var(--spacer-20);

		& + * {
			margin-top: var(--spacer-20);
		}
	}

	/* -----------------------------------------------------------------------------
	 * Headings
	 * ----------------------------------------------------------------------------- */

	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {

		&:not(:first-child) {
			margin-top: var(--spacer-50);
			margin-bottom: var(--spacer-20);
		}

		&:not(:last-child) {
			margin-bottom: var(--spacer-20);
		}
	}

	/* -----------------------------------------------------------------------------
	 * Horizontal Rule
	 * ----------------------------------------------------------------------------- */

	.block-editor-block-list__block[data-type="core/separator"].block-editor-block-list__block, /* Hack to overcome Core block editor styles. */
	hr {
		margin-top: var(--spacer-60);
		margin-bottom: var(--spacer-60);
	}

	/* -------------------------------------------------------------------------
	 * Lists
	 * ------------------------------------------------------------------------- */

	/* Shared */
	ul,
	ol,
	dl {
		margin-top: var(--spacer-40);
		margin-bottom: var(--spacer-40);
	}

	/* List Item */
	ol,
	ul {

		li {

			& ~ li,
			> ol,
			> ul {
				margin-top: var(--spacer-20);
			}

		}
	}

	/* Definition */
	dl {

		dd {
			margin-bottom: var(--spacer-30);
		}
	}

	/* -----------------------------------------------------------------------------
	 * Paragraph
	 * ----------------------------------------------------------------------------- */

	p {
		margin-bottom: var(--spacer-30);
	}

	/* -----------------------------------------------------------------------------
	 * Quote & Blockquote
	 * ----------------------------------------------------------------------------- */

	/* Shared */
	[data-type="core/quote"] > blockquote, /* Hack to overcome Core block editor styles. */
	[data-type="core/quote"] > q, /* Hack to overcome Core block editor styles. */
	blockquote,
	q {
		margin: var(--spacer-60) auto;
	}

	/* q */
	q {}

	/* blockquote */
	[data-type="core/quote"] > blockquote, /* Hack to overcome Core block editor styles. */
	blockquote {

		p {
			margin: 0 auto;

			& ~ p {
				margin-top: var(--spacer-40);
			}
		}
	}

	/* cite */
	cite,
	.wp-block-quote__citation {
		display: block;
		margin-top: var(--spacer-10);
	}

	/* -----------------------------------------------------------------------------
	 * Table
	 * ----------------------------------------------------------------------------- */

	table {
		margin: var(--spacer-60) auto 0;
	}
}
