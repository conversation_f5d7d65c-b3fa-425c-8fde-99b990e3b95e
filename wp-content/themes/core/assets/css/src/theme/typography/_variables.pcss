/* -----------------------------------------------------------------------------
 *
 * Variables: Typography
 *
 * ----------------------------------------------------------------------------- */

:root {
	/* -----------------------------------------------------------------------------
	 * Font Families
	 * ----------------------------------------------------------------------------- */

	--font-family-core-icons: 'core-icons';
	--font-family-serif: serif;
	--font-family-sans-serif: var(--font-family-poppins);
	--font-family-base: var(--global-font-family-secondary);
	--font-family-headings: var(--global-font-family-primary);

	/* -----------------------------------------------------------------------------
	 * Font Sizing
	 * ----------------------------------------------------------------------------- */

	--font-size-body-large: 24px;
	--font-size-body: 18px;
	--font-size-body-xxsmall: 11px;
	--font-size-body-xsmall: 12px;
	--font-size-body-small: 16px;
	--font-size-special-small: 14px;
	--font-size-heading-xxlarge: var(--global-font-size-heading-xxlarge);
	--font-size-heading-xlarge: var(--global-font-size-heading-xlarge);
	--font-size-heading-large: var(--global-font-size-heading-large);
	--font-size-heading: var(--global-font-size-heading);
	--font-size-heading-small: var(--global-font-size-heading-small);
	--font-size-heading-xsmall: var(--global-font-size-heading-xsmall);
	--font-size-heading-xxsmall: var(--global-font-size-heading-xxsmall);
	--font-size-heading-xxxsmall: var(--global-font-size-heading-xxxsmall);

	/* -----------------------------------------------------------------------------
	 * Line Heights
	 * ----------------------------------------------------------------------------- */

	--line-height-body: 1.6;
	--line-height-heading-xxlarge: var(--global-line-height-heading-xxlarge);
	--line-height-heading-xlarge: var(--global-line-height-heading-xlarge);
	--line-height-heading-large: var(--global-line-height-heading-large);
	--line-height-heading: var(--global-line-height-heading);
	--line-height-heading-small: var(--global-line-height-heading-small);
	--line-height-heading-xsmall: var(--global-line-height-heading-xsmall);
	--line-height-heading-xxsmall: var(--global-line-height-heading-xxsmall);
	--line-height-heading-xxxsmall: var(--global-line-height-heading-xxxsmall);

	/* -----------------------------------------------------------------------------
	 * Font Weights
	 * ----------------------------------------------------------------------------- */

	--font-weight-regular: 400;
	--font-weight-medium: 500;
	--font-weight-semibold: 600;
	--font-weight-bold: 700;
	--font-weight-heading-xxlarge: var(--global-font-weight-heading-xxlarge);
	--font-weight-heading-xlarge: var(--global-font-weight-heading-xlarge);
	--font-weight-heading-large: var(--global-font-weight-heading-large);
	--font-weight-heading: var(--global-font-weight-heading);
	--font-weight-heading-small: var(--global-font-weight-heading-small);
	--font-weight-heading-xsmall: var(--global-font-weight-heading-xsmall);
	--font-weight-heading-xxsmall: var(--global-font-weight-heading-xxsmall);
	--font-weight-heading-xxxsmall: var(--global-font-weight-heading-xxxsmall);

	/* -----------------------------------------------------------------------------
	 * Letter Spacing
	 * ----------------------------------------------------------------------------- */
	--letter-spacing-xlarge: 1.68px;
	--letter-spacing-large: 1px;
	--letter-spacing-regular: 0;


	/* -----------------------------------------------------------------------------
	 * Border Radius
	 * TODO: Where do these belong?
	 * ----------------------------------------------------------------------------- */

	--border-radius-small: 3px;
	--border-radius-base: 8px;
	--border-radius-buttons: var(--global-button-border-radius);
	--border-radius-media: var(--global-media-border-radius);
	--border-radius-highlights-small: 6px;
	--border-radius-highlights: 10px;
	--border-radius-circle: 100%;

	/* -----------------------------------------------------------------------------
	 * Color: Links
	 * ----------------------------------------------------------------------------- */

	--color-link: var(--color-primary);
	--color-link-hover: var(--color-primary-70);
	--color-link-focus: var(--color-primary-70);

	/* -----------------------------------------------------------------------------
	 * Text Transform
	 * ----------------------------------------------------------------------------- */
	--text-transform-buttons: var(--global-button-text-transform);
}
