/* -----------------------------------------------------------------------------
 *
 * Mixins: Typography
 *
 * ----------------------------------------------------------------------------- */

/* -------------------------------------------------------------------------
 * Display XX-Large
 * ------------------------------------------------------------------------- */

@define-mixin t-display-xx-large {
	font-family: var(--font-family-headings);
	font-size: var(--font-size-heading-xxlarge);
	line-height: var(--line-height-heading-xxlarge);
	font-weight: var(--font-weight-heading-xxlarge);
	letter-spacing: -0.9px;
}

/* -------------------------------------------------------------------------
 * Display X-Large
 * ------------------------------------------------------------------------- */

@define-mixin t-display-x-large {
	font-family: var(--font-family-headings);
	font-size: var(--font-size-heading-xlarge);
	line-height: var(--line-height-heading-xlarge);
	font-weight: var(--font-weight-heading-xlarge);
	letter-spacing: -0.9px;
}

/* -------------------------------------------------------------------------
 * Display Large
 * ------------------------------------------------------------------------- */

@define-mixin t-display-large {
	font-family: var(--font-family-headings);
	font-size: var(--font-size-heading-large);
	line-height: var(--line-height-heading-large);
	font-weight: var(--font-weight-heading-large);
	letter-spacing: -0.3px;
}

/* -------------------------------------------------------------------------
 * Display
 * ------------------------------------------------------------------------- */

@define-mixin t-display {
	font-family: var(--font-family-headings);
	font-size: var(--font-size-heading);
	line-height: var(--line-height-heading);
	font-weight: var(--font-weight-heading);
	letter-spacing: -0.2px;
}

/* -------------------------------------------------------------------------
 * Display Small
 * ------------------------------------------------------------------------- */

@define-mixin t-display-small {
	font-family: var(--font-family-headings);
	font-size: var(--font-size-heading-small);
	line-height: var(--line-height-heading-small);
	font-weight: var(--font-weight-heading-small);
	letter-spacing: -0.1px;
}

/* -------------------------------------------------------------------------
 * Display X-Small
 * ------------------------------------------------------------------------- */

@define-mixin t-display-x-small {
	font-family: var(--font-family-headings);
	font-size: var(--font-size-heading-xsmall);
	line-height: var(--line-height-heading-xsmall);
	font-weight: var(--font-weight-heading-xsmall);
}

/* -------------------------------------------------------------------------
 * Display XX-Small
 * ------------------------------------------------------------------------- */

@define-mixin t-display-xx-small {
	font-family: var(--font-family-headings);
	font-size: var(--font-size-heading-xxsmall);
	line-height: var(--line-height-heading-xxsmall);
	font-weight: var(--font-weight-heading-xxsmall);
}

/* -------------------------------------------------------------------------
 * Display XXX-Small
 * ------------------------------------------------------------------------- */

 @define-mixin t-display-xxx-small {
	font-family: var(--font-family-headings);
	font-size: var(--font-size-heading-xxxsmall);
	line-height: var(--line-height-heading-xxxsmall);
	font-weight: var(--font-weight-heading-xxxsmall);
}

/* -------------------------------------------------------------------------
 * Body Large
 * ------------------------------------------------------------------------- */

@define-mixin t-body-large {
	font-family: var(--font-family-base);
	font-size: var(--font-size-body-large);
	line-height: 1.5;
	font-weight: 400;
}

/* -------------------------------------------------------------------------
 * Body
 * ------------------------------------------------------------------------- */

@define-mixin t-body {
	font-family: var(--font-family-base);
	font-size: var(--font-size-body);
	line-height: 1.6;
	font-weight: var(--font-weight-regular);
}

/* -------------------------------------------------------------------------
 * Body Small
 * ------------------------------------------------------------------------- */

@define-mixin t-body-small {
	font-family: var(--font-family-base);
	font-size: var(--font-size-body-small);
	line-height: 1.5;
	font-weight: var(--font-weight-regular);
	letter-spacing: 0.1px;
}

/* -------------------------------------------------------------------------
 * Overline
 * ------------------------------------------------------------------------- */

@define-mixin t-overline {
	font-family: var(--font-family-base);
	font-size: 14px;
	line-height: 1.5;
	font-weight: var(--font-weight-semibold);
	letter-spacing: 1.5px;
	text-transform: uppercase;
}

/* -------------------------------------------------------------------------
 * Caption
 * ------------------------------------------------------------------------- */

@define-mixin t-caption {
	font-family: var(--font-family-base);
	font-size: var(--font-size-body-xsmall);
	line-height: 1.5;
	font-weight: var(--font-weight-regular);
	letter-spacing: 0.1px;
	font-style: italic;
}

/* -------------------------------------------------------------------------
 * Tooltip
 * ------------------------------------------------------------------------- */

@define-mixin t-tooltip {
	font-family: var(--font-family-base);
	font-size: var(--font-size-body-xxsmall);
	line-height: 1.67;
	font-weight: var(--font-weight-regular);
	letter-spacing: 0.1px;
}

/* -------------------------------------------------------------------------
 * Button
 * ------------------------------------------------------------------------- */

@define-mixin t-button {
	font-family: var(--font-family-base);
	font-size: 16px;
	line-height: 1.5;
	font-weight: var(--font-weight-semibold);
	letter-spacing: 0.02em;
}

/* -------------------------------------------------------------------------
 * Text Button/Call To Action
 * ------------------------------------------------------------------------- */

@define-mixin t-text-button {
	font-family: var(--font-family-base);
	font-size: 16px;
	line-height: 1.5;
	font-weight: var(--font-weight-semibold);
	letter-spacing: 0.1111px;
}

/* -------------------------------------------------------------------------
 * Input
 * ------------------------------------------------------------------------- */

@define-mixin t-input {
	font-family: var(--font-family-base);
	font-size: 16px;
	line-height: 1.5;
	font-weight: 400;
}

/* -------------------------------------------------------------------------
 * Label
 * ------------------------------------------------------------------------- */

@define-mixin t-label {
	font-family: var(--font-family-base);
	font-size: 14px;
	line-height: 1.4;
	font-weight: 400;
}

/* -------------------------------------------------------------------------
 * Helper Text
 * ------------------------------------------------------------------------- */

@define-mixin t-helper {
	font-family: var(--font-family-base);
	font-size: 11px;
	line-height: 16px;
	font-weight: 400;
	letter-spacing: 0.5px;
}

/* -------------------------------------------------------------------------
 * Quotes & Blockquotes
 * ------------------------------------------------------------------------- */

@define-mixin t-quote {
	quotes: '\201C' '\201D' '\2018' '\2019';
	font-size: 32px;
	font-weight: var(--font-weight-bold);
	line-height: 1.25;
	letter-spacing: -0.1px;
	color: var(--color-primary);

	&:first-child {
		text-indent: -0.5em;

		&:before {
			content: open-quote;
		}
	}

	&:last-of-type {

		&:after {
			content: close-quote;
		}
	}
}

/* -------------------------------------------------------------------------
 * Tag
 * ------------------------------------------------------------------------- */

@define-mixin t-tag {
	display: inline-block;
	background: var(--color-black);
	color: var(--color-white);
	padding: 4px 8px;
	text-transform: uppercase;
	font-size: 13px;
	letter-spacing: 0.5px;
}

/* -------------------------------------------------------------------------
 * Ellipses for text that should be constrained
 * ------------------------------------------------------------------------- */

@define-mixin t-ellipses $width {
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
	max-width: $width;

	@media (--viewport-medium) {
		text-overflow: unset;
		overflow: auto;
		white-space: unset;
		max-width: 100%;
	}
}

/* -------------------------------------------------------------------------
 * Pill
 * ------------------------------------------------------------------------- */

@define-mixin t-pill {
	display: inline-flex;
	background: var(--color-primary);
	color: var(--color-white);
	padding: 4px var(--spacer-20);
	text-transform: uppercase;
	font-size: 14px;
	font-weight: var(--font-weight-bold);
	border-radius: 100px;
}

/* -------------------------------------------------------------------------
 * Text alignment
 * ------------------------------------------------------------------------- */

@define-mixin t-text-align {

	&.has-text-align-center {
		text-align: center;
	}

	&.has-text-align-left {
		text-align: left;
	}

	&.has-text-align-right {
		text-align: right;
	}
}

/* -------------------------------------------------------------------------
 * Quote Text Large
 * ------------------------------------------------------------------------- */

@define-mixin t-quote-text-large {
	@mixin t-display-small;
	font-family: var(--font-family-base);

	@media (--viewport-full) {
		@mixin t-display;
		font-family: var(--font-family-base);
	}
}

/* -------------------------------------------------------------------------
 * Quote Text Medium
 * ------------------------------------------------------------------------- */

@define-mixin t-quote-text-medium {
	@mixin t-display-small;
	font-family: var(--font-family-base);
}

/* -------------------------------------------------------------------------
 * Quote Text Small
 * ------------------------------------------------------------------------- */

@define-mixin t-quote-text-small {
	@mixin t-display-x-small;
	font-family: var(--font-family-base);
}
