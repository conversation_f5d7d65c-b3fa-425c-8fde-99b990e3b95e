/* -----------------------------------------------------------------------------
 *
 * Typography: Headings
 *
 * ----------------------------------------------------------------------------- */

/* -------------------------------------------------------------------------
 * Heading: H1
 * ------------------------------------------------------------------------- */

.h1,
.t-sink > h1,
.t-sink > .wp-block-group h1,
.t-sink > .wp-block-group .h1 {
	@mixin t-display-large;
	@mixin t-text-align;

	@media (--viewport-medium) {
		@mixin t-display-x-large;
	}

	strong, b {
		font-weight: inherit;
	}

	> span {
		font-weight: inherit !important;
	}
}

/* -------------------------------------------------------------------------
 * Heading: H2
 * ------------------------------------------------------------------------- */

.h2,
.t-sink > h2,
.t-sink > .wp-block-group h2,
.t-sink > .wp-block-group .h2 {
	@mixin t-display;
	@mixin t-text-align;
	letter-spacing: -.2px;

	@media (--viewport-medium) {
		@mixin t-display-large;
	}

	strong, b {
		font-weight: inherit;
	}

	> span {
		font-weight: inherit !important;
	}
}

/* -------------------------------------------------------------------------
 * Heading: H3
 * ------------------------------------------------------------------------- */

.h3,
.t-sink > h3,
.t-sink > .wp-block-group h3,
.t-sink > .wp-block-group .h3 {
	@mixin t-display-small;
	@mixin t-text-align;
	letter-spacing: -.1px;

	@media (--viewport-medium) {
		@mixin t-display;
	}

	strong, b {
		font-weight: inherit;
	}

	> span {
		font-weight: inherit !important;
	}
}

/* -------------------------------------------------------------------------
 * Heading: H4
 * ------------------------------------------------------------------------- */

.h4,
.t-sink > h4,
.t-sink > .wp-block-group h4,
.t-sink > .wp-block-group .h4 {
	@mixin t-display-x-small;
	@mixin t-text-align;
	letter-spacing: 0;

	@media (--viewport-medium) {
		@mixin t-display-small;
	}

	strong, b {
		font-weight: inherit;
	}

	> span {
		font-weight: inherit !important;
	}
}

/* -------------------------------------------------------------------------
 * Heading: H5
 * ------------------------------------------------------------------------- */

.h5,
.t-sink > h5,
.t-sink > .wp-block-group h5,
.t-sink > .wp-block-group .h5 {
	@mixin t-display-xxx-small;
	@mixin t-text-align;
	letter-spacing: 0;

	@media (--viewport-medium) {
		@mixin t-display-x-small;
	}

	strong, b {
		font-weight: inherit;
	}

	> span {
		font-weight: inherit !important;
	}
}

/* -------------------------------------------------------------------------
 * Heading: H6
 * ------------------------------------------------------------------------- */

.h6,
.t-sink > h6,
.t-sink > .wp-block-group h6,
.t-sink > .wp-block-group .h6 {
	@mixin t-overline;
	@mixin t-text-align;

	strong, b {
		font-weight: inherit;
	}

	> span {
		font-weight: inherit !important;
	}
}
