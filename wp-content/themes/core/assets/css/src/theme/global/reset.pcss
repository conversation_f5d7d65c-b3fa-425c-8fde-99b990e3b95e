/* -----------------------------------------------------------------------------
 *
 * Global "Resets"
 *
 * ----------------------------------------------------------------------------- */

* {
	box-sizing: border-box;
}

@viewport {
	width: device-width;
}

/* -----------------------------------------------------------------------------
 *
 * HTML5 Elements
 *
 * ----------------------------------------------------------------------------- */

article,
aside,
details,
figcaption,
figure,
footer,
header,
main,
menu,
nav,
section,
summary {
	display: block;
}

/* -----------------------------------------------------------------------------
 *
 * Normalize "Light"
 *
 * See normalize source code for clarity on rules
 * https://github.com/necolas/normalize.css
 *
 * ----------------------------------------------------------------------------- */

html {
	text-size-adjust: 100%;
	font-size: 100%;
	-webkit-tap-highlight-color: transparent;
}

body {
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	font-smoothing: antialiased;
	min-width: 320px;
	overflow-x: hidden;
}

figure {
	line-height: 0;
}

figcaption {
	line-height: normal;
}

svg:not(:root) {
	overflow: hidden;
}

audio,
canvas,
progress,
video {
	display: inline-block;
}

audio:not([controls]) {
	display: none;
	height: 0;
}

progress {
	vertical-align: baseline;
}

template,
[hidden] {
	display: none;
}

a {
	background-color: transparent;
	-webkit-text-decoration-skip: objects;
}

abbr[title] {
	border-bottom: none;
	text-decoration: underline;
	text-decoration: underline dotted;
}

pre {
	overflow: auto;
}

code,
kbd,
pre,
samp {
	font-family: monospace;
	font-size: 1em;
}

b,
strong {
	font-weight: inherit;
	font-weight: var(--font-weight-semibold);
}

dfn {
	font-style: italic;
}

mark {
	background-color: #ff0;
	color: #000;
}

small {
	font-size: 80%;
}

sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}

sup {
	top: -0.5em;
}

sub {
	bottom: -0.25em;
}

hr {
	border: 0;
	height: 0;
}

address,
cite {
	font-style: normal;
}


/* Input: Kill browser input chrome */
input[type="text"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="submit"],
input[type="password"],
input[type="reset"],
input[type="button"],
button,
textarea {
	appearance: none;
}

input,
button,
select,
textarea,
optgroup {
	color: inherit;
	font: inherit;
	line-height: normal;
	-webkit-font-smoothing: antialiased;
}

input,
button,
select,
textarea {
	outline: 0;
	box-sizing: border-box;
	margin: 0;
	border-radius: 0;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
	height: auto;
}

select {

	&:-moz-focusring {
		color: transparent;
		text-shadow: 0 0 0 #000;
	}
}

legend {
	color: inherit;
	display: table;
	max-width: 100%;
	white-space: normal;
}

textarea {
	resize: none;
	overflow: auto;
}

optgroup {
	font-weight: bold;
}

button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
	cursor: pointer;
	overflow: visible;
}

button[disabled],
html input[disabled] {
	cursor: default;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
	border: 0;
	padding: 0;
}

/* -----------------------------------------------------------------------------
 *
 * Reset "Light"
 *
 * ----------------------------------------------------------------------------- */

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
main,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
	margin: 0;
	padding: 0;
	border: 0;
}

ol,
ul {
	list-style: none;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
	font-weight: normal;
	text-rendering: optimizeLegibility;
	line-height: var(--line-height-body);
}

img {
	-ms-interpolation-mode: bicubic;
	height: auto;
	max-width: 100%;
	border-style: none;
}

iframe,
video,
embed {
	max-width: 100%;
	max-height: 100%;
}
