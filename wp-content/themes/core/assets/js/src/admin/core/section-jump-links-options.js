/**
 * @module
 * @exports section-jump-links-options
 * @description Add options to the section jump links
 */

import * as tools from "utils/tools";
import { on } from "utils/events";
import { convertJsonToQueryString, slugify } from "../../utils/tools";

const el = {
	isPostPage: tools.getNodes(".post-php", true, document, true)[0],
	isNewPostPage: tools.getNodes(".post-new-php", true, document, true)[0],
	isToolPost: tools.getNodes(
		".post-type-tribe_tool_post",
		true,
		document,
		true
	)[0],
	isServicePost: tools.getNodes(
		".post-type-tribe_service_post",
		true,
		document,
		true
	)[0],
	isListiclePost: false,
	clearButton: tools.getNodes(
		"#acf-section-jump-links-option-clear",
		true,
		document,
		true
	)[0],
	populateButtonH2: tools.getNodes(
		"#acf-section-jump-links-option-populate-h2",
		true,
		document,
		true
	)[0],
	populateButtonH3: tools.getNodes(
		"#acf-section-jump-links-option-populate-h3",
		true,
		document,
		true
	)[0],
	rewriteButton: tools.getNodes(
		"#acf-section-jump-links-option-rewrite",
		true,
		document,
		true
	)[0],
	mainAddButton: tools.getNodes(
		'[data-name="section_jump_links"] .acf-repeater > .acf-actions [data-event="add-row"]',
		true,
		document,
		true
	)[0],
	acfRepeater: tools.getNodes(
		'[data-name="section_jump_links"] .acf-repeater',
		true,
		document,
		true
	)[0],
	acfTable: tools.getNodes(
		'[data-name="section_jump_links"] .acf-table tbody',
		true,
		document,
		true
	)[0],
	cloneRow: tools.getNodes(
		'[data-name="section_jump_links"] .acf-table .acf-clone',
		true,
		document,
		true
	)[0],
};

const pubReviewAnchors = {
	"acf/sslprovidersummary": "summary",
	"acf/whyyoucantrust": "why-trust",
	"acf/sslprovideroverview": "overview",
	"acf/sslprovidermarketfit": "use-cases",
	"acf/ourreviewmethodology": "methodology",
	"acf/sslprovideranalystreviews": "analyst-reviews",
	"acf/sslproviderdetails": "specs",
	"acf/sslprovideralternatives": "alternatives",
	"acf/sslpubreviewfaqs": "faq",
	"acf/sslproviderfaqs": "tool-faq",
	"acf/sslprovidercompanyhistory": "company",
};

const controlElementsVisibility = () => {
	const sectionJumpLinks = tools.getNodes(
		'[data-name="section_jump_links"] .acf-table .acf-row:not(.acf-clone)',
		true,
		document,
		true
	);

	if (sectionJumpLinks.length === 0) {
		el.clearButton.style.display = "none";
		el.populateButtonH2.style.removeProperty("display");
		el.populateButtonH3.style.removeProperty("display");
		el.rewriteButton.style.display = "none";
		el.acfRepeater.classList.add("-empty");
	} else {
		el.clearButton.style.removeProperty("display");
		el.populateButtonH2.style.display = "none";
		el.populateButtonH3.style.display = "none";
		el.rewriteButton.style.removeProperty("display");
		el.acfRepeater.classList.remove("-empty");
	}
};

const handleClear = () => {
	// eslint-disable-next-line no-alert
	const confirm = window.confirm(
		"Are you sure you want to clear the Table Of Contents links?\nIMPORTANT: This cannot be undone."
	);

	if (!confirm) {
		return;
	}

	const sectionJumpLinks = tools.getNodes(
		'[data-name="section_jump_links"] .acf-table .acf-row:not(.acf-clone)',
		true,
		document,
		true
	);

	sectionJumpLinks.forEach((sectionJumpLink) => {
		sectionJumpLink.remove();
	});

	controlElementsVisibility();
};

const addToJumpLinksACF = (anchor, heading) => {
	el.mainAddButton.click();

	const newTableLine = tools.getNodes(
		'[data-name="section_jump_links"] .acf-table .acf-row:nth-last-child(1 of :not(.acf-clone))',
		true,
		document,
		true
	)[0];
	const inputBlockId = tools.getNodes(
		'[data-name="block_id"] input',
		true,
		newTableLine,
		true
	)[0];
	const inputLabel = tools.getNodes(
		'[data-name="label"] input',
		true,
		newTableLine,
		true
	)[0];

	inputBlockId.value = anchor;

	if (!heading) {
		return;
	}

	inputLabel.value = heading
		.replaceAll("&amp;", "and")
		.replace(/(<([^>]+)>)/gi, "") // Stripping all HTML native tags
		.replace(/&lt;.*&gt;/gi, "") // Stripping all HTML ASCII tags ( "<" and ">" )
		.replace(/^\d+(\.\d+)*\.\s/, ""); // Stripping to remove any "number. " at the beginning of the string
};

const updateBlockId = (block, anchor) => {
	// eslint-disable-next-line no-undef
	const _wp = wp;

	_wp.data.dispatch("core/block-editor").updateBlockAttributes(block.clientId, {
		...block.attributes,
		anchor,
	});
};

function getAnchor(block, content = null) {
	let anchor = block.attributes.anchor;

	if (!anchor) {
		if (Object.prototype.hasOwnProperty.call(pubReviewAnchors, block.name)) {
			anchor = pubReviewAnchors[block.name];

			updateBlockId(block, anchor);
		} else if (content) {
			anchor = slugify(content);
		} else {
			anchor = slugify(block.attributes.content);
		}

		updateBlockId(block, anchor);
	}

	return anchor;
}

const generateJumpLinkFromHeading = (heading, level = 2) => {
	if (heading.attributes.level > level) {
		return;
	}

	const anchor = getAnchor(heading);

	if (!heading.attributes.content) {
		return;
	}

	addToJumpLinksACF(anchor, heading.attributes.content);
};

const generateJumpLinkFromAdvancedAccordion = (advancedAccordion) => {
	// Remove span tags in case there is rating in the title
	const title = advancedAccordion.attributes.data.title
		.replace(/<span[^>]*>[\s\S]*?<\/span>/g, "")
		.trim();
	const anchor = getAnchor(advancedAccordion, title);

	addToJumpLinksACF(anchor, title);
};

const populateFromHeadings = (headingsInTheEditor, level) => {
	headingsInTheEditor.forEach((heading) =>
		generateJumpLinkFromHeading(heading, level)
	);
};

const populateFromChapters = (chaptersInTheEditor) => {
	chaptersInTheEditor.forEach((chapter) => {
		const anchor = slugify(chapter.attributes.data.chapter_title);

		addToJumpLinksACF(anchor, chapter.attributes.data.chapter_title);
	});
};

const handlePopulatePost = (level) => {
	// eslint-disable-next-line no-undef
	const _wp = wp;

	const headingsInTheEditor = _wp.data
		.select("core/block-editor")
		.getBlocks()
		.filter((b) => b.name === "core/heading");
	const chaptersInTheEditor = _wp.data
		.select("core/block-editor")
		.getBlocks()
		.filter((b) => b.name === "acf/contentchapter");

	if (chaptersInTheEditor.length > 0) {
		populateFromChapters(chaptersInTheEditor);
	} else {
		if (!headingsInTheEditor) {
			return;
		}

		populateFromHeadings(headingsInTheEditor, level);
	}

	controlElementsVisibility();
};

const handlePopulateToolPost = (level) => {
	// eslint-disable-next-line no-undef
	const _wp = wp;

	const blocks = _wp.data
		.select("core/block-editor")
		.getBlocks()
		.filter((b) => {
			return (
				b.name === "core/heading" ||
				b.name === "acf/sslfull-listing" ||
				b.name === "acf/accordionadvanced" ||
				b.name === "acf/sslprovider-analyst-reviews" ||
				b.name === "acf/whyyoucantrust" ||
				b.name === "acf/gridlist" ||
				b.name === "acf/ssladvancedlisting" ||
				b.name.includes("acf/sslxvsy") ||
				b.name.includes("acf/sslprovider") ||
				b.name.includes("acf/sslpubreview") ||
				b.name.includes("acf/sslprovidersummary") ||
				b.name.includes("acf/sslprovideroverview") ||
				b.name.includes("acf/sslprovidermarketfit") ||
				b.name.includes("acf/ourreviewmethodology") ||
				b.name.includes("acf/sslprovideranalystreviews") ||
				b.name.includes("acf/sslproviderdetails") ||
				b.name.includes("acf/sslprovideralternatives") ||
				b.name.includes("acf/sslpubreviewfaqs") ||
				b.name.includes("acf/sslproviderfaqs") ||
				b.name.includes("acf/sslprovidercompanyhistory")
			);
		});

	if (!blocks) {
		return;
	}

	blocks.forEach((block) => {
		const customTitle = tools.getNodes(
			"#block-" + block.clientId + " .c-block[data-toc-title]",
			true,
			document,
			true
		)[0];

		if (Object.prototype.hasOwnProperty.call(pubReviewAnchors, block.name)) {
			const anchor = getAnchor(block);

			if (!customTitle) {
				return;
			}

			addToJumpLinksACF(anchor, customTitle.getAttribute("data-toc-title"));

			return;
		}

		if (customTitle && customTitle.getAttribute("data-toc-title")) {
			const anchor = getAnchor(
				block,
				customTitle.getAttribute("data-toc-title")
			);

			addToJumpLinksACF(anchor, customTitle.getAttribute("data-toc-title"));

			if (block.name === "acf/ssladvancedlisting") {
				// Table_of_Contents_Block_Controller::TOC_TEMPLATE_TAG
				addToJumpLinksACF("", "{[toc-full-listing-tools]}");
			}

			return;
		}

		if (block.name === "acf/sslfull-listing") {
			const anchor = getAnchor(block, block.attributes.data.title);

			addToJumpLinksACF(anchor, block.attributes.data.title);

			// Table_of_Contents_Block_Controller::TOC_TEMPLATE_TAG
			addToJumpLinksACF("", "{[toc-full-listing-tools]}");

			return;
		} else if (block.name === "acf/accordionadvanced") {
			generateJumpLinkFromAdvancedAccordion(block);

			return;
		} else if (block.name === "acf/sslprovider-analyst-reviews") {
			block.innerBlocks.forEach((innerBlock) => {
				if (innerBlock.name === "acf/accordionadvanced") {
					generateJumpLinkFromAdvancedAccordion(innerBlock);
				}
			});

			return;
		}

		generateJumpLinkFromHeading(block, level);
	});

	controlElementsVisibility();
};

const handleRemoveRow = () => {
	const addNewButtons = tools.getNodes(
		'[data-name="section_jump_links"] [data-event="remove-row"]',
		true,
		document,
		true
	);

	addNewButtons.forEach((addNewButton) => {
		on(addNewButton, "click", handleRemoveRow);
	});

	setTimeout(() => {
		const confirmButton = tools.getNodes(
			'.acf-tooltip [data-event="confirm"]',
			true,
			document,
			true
		)[0];

		if (confirmButton) {
			on(confirmButton, "click", handleRemoveRow);
		}

		controlElementsVisibility();
	}, 500);
};

const handleAddNewRow = () => {
	const addNewButtons = tools.getNodes(
		'[data-name="section_jump_links"] [data-event="add-row"]',
		true,
		document,
		true
	);

	addNewButtons.forEach((addNewButton) => {
		on(addNewButton, "click", handleAddNewRow);
	});

	controlElementsVisibility();

	setTimeout(() => {
		handleRemoveRow();
	}, 500);
};

const handleRewrite = async () => {
	const sectionJumpLinks = tools.getNodes(
		'[data-name="section_jump_links"] .acf-table .acf-row:not(.acf-clone)',
		true,
		document,
		true
	);

	const emptyJumpLinks = sectionJumpLinks.filter((v) => {
		const linkInput = tools.getNodes(
			'[data-name="label"] input',
			true,
			v,
			true
		)[0];

		return linkInput.value === "";
	});

	if (emptyJumpLinks.length > 0) {
		// eslint-disable-next-line no-alert
		alert(`One or more labels of the Table Of Contents below are empty.`);
		return;
	}

	// eslint-disable-next-line no-alert
	const confirm = window.confirm(
		"Are you sure you want to rewrite the Table Of Contents labels?\nIMPORTANT: This cannot be undone."
	);

	if (!confirm) {
		return;
	}

	tools.addClass(el.rewriteButton, "section-jump-links-options-loading");
	el.rewriteButton.disabled = true;

	const labelInputs = tools.getNodes(
		'[data-name="section_jump_links"] .acf-table .acf-row:not(.acf-clone) [data-name="label"] input',
		true,
		document,
		true
	);

	const labels = labelInputs.map((labelInput) => {
		return {
			id: labelInput.id,
			value: labelInput.value.replaceAll("&", "and").replace(/[<>/"'=]/g, ""),
		};
	});

	const labelsToString = labels.reduce((acc, label) => {
		return acc + `\n\nID: ${label.id}\n Title: ${label.value}`;
	}, "");

	// eslint-disable-next-line no-undef
	const _nonce = bwz_back_office.nonce;

	// eslint-disable-next-line no-undef
	const _ajaxurl = ajaxurl;

	const data = {
		nonce: _nonce,
		action: "bwz_openai_generate",
		context: "You are writing the summary links for a blog post",
		prompt: `Convert each title to sentence case. Ensure that proper nouns and acronyms that are usually capitalized remain capitalized. Do not rewrite the title in any other way. Return only the response as a JSON using the ID as key and the created cta as the value. The starting titles are: ${labelsToString}; OUTPUT — JSON only, no markdown, no commentary`,
		model: "gpt-4o-mini",
	};

	try {
		const response = await fetch(_ajaxurl, {
			method: "POST",
			headers: { "Content-Type": "application/x-www-form-urlencoded" },
			credentials: "same-origin",
			body: convertJsonToQueryString(data),
		});

		if (!response.ok) {
			throw new Error("Open AI did not respond correctly. Please try again.");
		}

		const responseJson = await response.json();

		if (!responseJson) {
			throw new Error("No response. Please try again.");
		}

		const content = JSON.parse(responseJson.data.ai_response);

		if (!content) {
			throw new Error("No content parsed. Please try again.");
		}

		labels.forEach((label) => {
			if (!content[label.id]) {
				return;
			}

			const labelInput = tools.getNodes(
				`#${label.id}`,
				true,
				document,
				true
			)[0];
			labelInput.value = content[label.id];
		});
	} catch (error) {
		console.error(error);
		// eslint-disable-next-line no-alert
		alert(`ERROR: Something went wrong.\n\n${error.message}`);
	} finally {
		el.rewriteButton.disabled = false;
		tools.removeClass(el.rewriteButton, "section-jump-links-options-loading");
	}
};

const checkIfTheIdsExistsOnSave = () => {
	// eslint-disable-next-line no-undef
	const _wp = wp;

	let isNoticeDisplayed = false;
	let wasSavingPost = _wp.data.select("core/editor").isSavingPost();
	let wasAutosavingPost = _wp.data.select("core/editor").isAutosavingPost();

	_wp.data.subscribe(function () {
		// reference https://github.com/WordPress/gutenberg/issues/17632
		const isSavingPost = _wp.data.select("core/editor").isSavingPost();
		const isAutosavingPost = _wp.data.select("core/editor").isAutosavingPost();
		const isDoneSaving = wasSavingPost && !isSavingPost && !wasAutosavingPost;
		wasSavingPost = isSavingPost;
		wasAutosavingPost = isAutosavingPost;

		if (isDoneSaving) {
			const sectionJumpLinks = document.querySelectorAll(
				'[data-name="section_jump_links"] .acf-row:not(.acf-clone) [data-name="block_id"] input'
			);

			if (!sectionJumpLinks) {
				return;
			}

			const idsFromChapters = [];
			const missingAnchors = [];
			const postContent = _wp.data.select("core/editor").getEditedPostContent();
			const regex =
				/<!-- wp:acf\/contentchapter(.*?)chapter_title":"(.*?)"(.*?)-->/g;
			let match;

			if (isNoticeDisplayed) {
				isNoticeDisplayed = false;
				_wp.data
					.dispatch("core/notices")
					.removeNotice("section-jump-links-options-missing-ids");
			}

			while ((match = regex.exec(postContent)) !== null) {
				idsFromChapters.push(slugify(match[2]));
			}

			sectionJumpLinks.forEach((input) => {
				const blockId = input.value;

				if (blockId === "") {
					return;
				}

				if (
					!postContent.includes(`id="${blockId}"`) &&
					!postContent.includes(`"anchor":"${blockId}"`) &&
					!idsFromChapters.includes(blockId)
				) {
					missingAnchors.push(blockId);
				}
			});

			if (missingAnchors.length > 0) {
				if (!isNoticeDisplayed) {
					isNoticeDisplayed = true;

					const missingIdsString = missingAnchors.join(", ");
					_wp.data
						.dispatch("core/notices")
						.createNotice(
							"error",
							`The post was saved but the following IDs in the Table of Contents are missing in the content as anchors: ${missingIdsString}`,
							{
								id: "section-jump-links-options-missing-ids",
								isDismissible: false,
								actions: [
									{
										label: "Go to the Table Of Contents Links",
										url: "#acf-section-jump-links-option-anchor",
									},
								],
							}
						);
				}
			}
		}
	});
};

const bindEvents = () => {
	on(el.clearButton, "click", handleClear);
	on(el.rewriteButton, "click", handleRewrite);

	if (el.isListiclePost) {
		on(el.populateButtonH2, "click", () => handlePopulateToolPost(2));
		on(el.populateButtonH3, "click", () => handlePopulateToolPost(3));
	} else {
		on(el.populateButtonH2, "click", () => handlePopulatePost(2));
		on(el.populateButtonH3, "click", () => handlePopulatePost(3));
	}

	// eslint-disable-next-line no-undef
	wp.domReady(() => {
		checkIfTheIdsExistsOnSave();
	});
};

/**
 * Initialize the remove delete menu script
 */
const sectionJumpLinksOptions = () => {
	if (
		!(el.isPostPage || el.isNewPostPage) ||
		!el.clearButton ||
		!el.populateButtonH2 ||
		!el.populateButtonH3 ||
		!el.rewriteButton
	) {
		return;
	}

	if (el.isToolPost || el.isServicePost) {
		el.isListiclePost = true;
	}

	bindEvents();

	handleAddNewRow();

	handleRemoveRow();

	controlElementsVisibility();

	console.info("Initialized Square One: Section Jump Links Options");
};

export default sectionJumpLinksOptions;
