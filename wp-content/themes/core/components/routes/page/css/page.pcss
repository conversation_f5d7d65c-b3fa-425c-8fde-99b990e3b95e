/* -----------------------------------------------------------------------------
 *
 * Component: Quote
 *
 * ----------------------------------------------------------------------------- */

.page {
	/* Style first block h2 to appear as h1 */
	#main-content > .l-sink {

		> :first-child:is(.c-block):not(.b-card-grid):not(.b-content-loop):not(.b-content-columns):not(.b-columns):not(.b-product-cards) {
			h2 {
				@media (--viewport-full) {
					@mixin t-display-x-large;
				}
			}
		}

		> :first-child:is(.wp-block-group) {

			> .wp-block-group__inner-container {

				> .c-block:first-of-type {
					h2 {
						@mixin t-display-x-large;

						@media (--viewport-full) {
							@mixin t-display-xx-large;
						}
					}
				}
			}
		}

		> .wp-block-group {

			> .wp-block-group__inner-container {

				> .c-block {
					&.b-content-loop--layout_single_left,
					&.b-content-loop--layout_single_right {
						h2 {
							@media (--viewport-full) {
								@mixin t-display-x-large;
							}
						}
					}
				}
			}
		}
	}

	/* CASE: Ensure we have padding if the first block on a page is a paragraph */
	#main-content > .t-sink {

		> :first-child:is(.c-block, p, h1, h2, h3, h4, h5, h6, .wp-block-image, .wp-block-embed, img, .gform_wrapper, .yoast-breadcrumbs) {
			padding-top: 80px;

			@media (--viewport-medium) {
				padding-top: 120px;
			}
		}

		> :last-child:is(.c-block, p, .wp-block-image, .wp-block-embed, img, .yoast-breadcrumbs, .b-buttons) {
			padding-bottom: 80px;

			@media (--viewport-medium) {
				padding-bottom: 120px;
			}
		}

		> :first-child:is(.b-text-separator) {
			@media (--viewport-medium) {
				margin-top: 0;
			}
		}

		> :first-child:is(.b-content-loop),
		> :first-child:is(.b-product-cards) {
			@media (--viewport-medium) {
				padding-top: 80px;
			}
		}
	}
}
