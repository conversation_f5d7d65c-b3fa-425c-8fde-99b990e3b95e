.post-template-single-template_editorial {
	.post-navigation-bar {
		@media (--viewport-full-max) {
			display: none;
		}
	}

	&:not(.has-header-ad) {
		--spacer-ad-push: 0px;

		.l-wrapper {
			margin-top: 0;

			@media (--viewport-large) {
				margin-top: 0;
			}
		}
	}

	.site-header-editorial {
		@media (--viewport-full-max) {
			.site-header-editorial__logo img {
				display: flex;
			}
		}

		&__icons-wrap {
			.site-header-editorial__scroll_nav-cta-wrapper {
				transform: translateY(-100%);
				max-height: 0;
				height: auto;
				overflow: hidden;

				@media (--viewport-small-max) {
					display: none;
				}
			}

			&.scroll-editable-nav-cta-show {
				.site-header-editorial__scroll_nav-cta-wrapper {
					transform: translateY(0);
					max-height: 100px;
					width: auto;
					overflow: visible;
				}
			}
		}
	}
}

.item-single--editorial {
	counter-reset: chapter-counter 0;

	.item-single {
		&__subheader-wrapper {
			min-height: 100vh;
			position: relative;
			padding: calc(var(--spacer-80) - var(--spacer-30) + 63px) 0 var(--spacer-70);
			margin-bottom: var(--spacer-50);
			display: flex;

			@media (--viewport-xxsmall) {
				height: auto;
			}

			@media (--viewport-small) {
				padding: calc(var(--spacer-80) - var(--spacer-30) + 93px) 0 var(--spacer-70);
			}

			@media (--viewport-large) {
				padding: calc(var(--spacer-80) - var(--spacer-30) + 115px) 0 var(--spacer-70);
			}

			&:after {
				content: "";
				display: block;
				position: absolute;
				top: 0;
				bottom: 0;
				left: 0;
				right: 0;
				background: var(--color-white);
				background: linear-gradient(90deg, var(--color-white) 0%, rgba(0, 0, 0, 0) 100%);

				@media (--viewport-large) {
					display: none;
				}
			}

			&.has-dark-background-color:after {
				display: block;
				background: var(--color-background-dark);
				background: linear-gradient(90deg, var(--color-background-dark) 0%, rgba(0, 0, 0, 0) 100%);
			}

			> .l-container {
				margin-top: 0;
				margin-left: 0;
				padding-left: var(--spacer-30);
				padding-right: var(--spacer-30);
				max-width: var(--grid-8-col);
				z-index: 1;
				position: relative;

				@media (--viewport-large) {
					padding-left: var(--spacer-40);
					padding-right: var(--spacer-40);
				}
			}

			> .l-container,
			.item-single__subheader-container,
			.item-single__post-meta {
				display: flex;
				align-items: stretch;
				flex-direction: column;
				flex: 1;
			}

			> .l-container {
				.item-single__category-meta {
					justify-content: flex-start;

					.item-single__category-link {
						color: var(--color-text);
						transition: var(--transition);

						&:hover, &:focus {
							text-decoration: underline;
						}
					}
				}

				.item-single__post-meta .page-title {
					text-align: left;
				}

				.c-post-info {
					.c-post-info__author-name {
						color: var(--color-text);
						transition: var(--transition);

						&:hover, &:focus {
							text-decoration: underline;
						}
					}

					.item-single__author-image {
						@media (--viewport-medium-max) {
							display: unset;
						}
					}

					&--multiple-authors {
						.item-single__author-image {
							&:not(:first-child) {
								margin-left: -15px;
							}
						}
					}
				}
			}

			p.item-single__excerpt-content {
				@media (--viewport-medium-max) {
					font-size: 16px;
				}
			}

			&.has-dark-background-color {
				> .l-container {
					.item-single__category-meta {
						.item-single__category-link {
							color: var(--color-white);
						}
					}

					.c-post-info {
						.c-post-info__author-name {
							color: var(--color-white);
						}
					}
				}
			}
		}

		&__subheader-background {
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			z-index: 0;
			background-position: center;
			background-repeat: no-repeat;
			background-size: cover;

			.c-lottie-player,
			.c-lottie-player__player {
				height: 100%;
			}
		}

		&__subheader-media {
			display: flex;
			justify-content: center;
			margin-top: var(--spacer-50);
		}

		&__post-meta {
			display: flex;
			flex-direction: column;

			@media (--viewport-medium) {
				padding: 0;
			}

			.item-single__excerpt {
				margin-bottom: var(--spacer-50);

				p.item-single__excerpt-content {
					@mixin t-body;
				}
			}

			.item-single__disclaimer {
				margin-top: var(--spacer-30);
				margin-bottom: 0;
			}

			.c-post-info {
				margin-top: var(--spacer-50);
				margin-bottom: 0;

				padding-top: var(--spacer-50);

				display: grid;
				grid-template-columns: 1fr;
				grid-template-rows: repeat(2, auto);
				grid-column-gap: var(--spacer-20);
				grid-row-gap: var(--spacer-10);
				align-items: center;

				@media (--viewport-small) {
					grid-template-columns: auto 1fr;
					grid-row-gap: 0;
				}

				&__divider {
					display: none;
				}

				/* custom media query */
				@media (min-width: 380px) {
					margin-top: auto;
				}

				.item-single__author-image {
					@media (--viewport-small) {
						grid-area: 1 / 1 / 3 / 2;
					}
				}

				.c-post-info__author-container {
					@media (--viewport-small) {
						grid-area: 1 / 2 / 2 / 3;
					}
				}

				.c-post-info__post-date-container {
					align-self: flex-start;
					justify-content: flex-start;
					align-items: flex-start;
					flex-direction: column;
					gap: 2px;

					@media (--viewport-small) {
						grid-area: 2 / 2 / 3 / 3;
					}

					@media (--viewport-medium) {
						flex-direction: row;
						gap: 8px;
					}

					.c-post-info__divider {
						display: none;
						line-height: 1.1;

						@media (--viewport-medium) {
							display: block;
						}
					}
				}

				&.hide-author {
					.c-post-info__post-date-container {
						grid-area: 1 / 1 / 3 / 3;
					}
				}

				&.hide-date {
					.c-post-info__author-container {
						grid-area: 1 / 2 / 3 / 3;
					}
				}

				.c-image__image.item-single__author-image-img {
					width: 64px;
					height: 64px;
				}

				&__author-name,
				&__publishing-date {
					font-size: 14px;
					text-align: left;
				}

				&__publishing-date {
					display: block;
				}
			}
		}

		&__content {
			margin-top: var(--spacer-50);

			> .c-ad-block-inline {
				&:first-of-type {
					margin-bottom: calc(var(--spacer-80) - var(--spacer-20));
				}
			}

			> section.wp-block-group.has-background {
				&:first-of-type {
					margin-top: calc(var(--spacer-50) * -1);
				}
			}
		}
	}
}

.site-header {
	&.hide-nav {
		opacity: 0;
		transform: translateY(calc(-100% - 10px - var(--spacer-ad-push)));
		transition: var(--transition);

		&.is-open {
			height: 100%;

			@media (--viewport-medium-max) {
				position: fixed !important;
			}
		}

		&.is-visible ~ .site-header-placeholder {
			@media (--viewport-medium-max) {
				display: none;
			}
		}
	}

	&-editorial {
		background: transparent;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		z-index: 10;
		display: grid;
		grid-template-columns: auto 1fr;
		align-items: center;
		transition: var(--transition);

		padding: var(--spacer-30);

		@media (--viewport-large) {
			padding-left: var(--spacer-40);
			padding-right: var(--spacer-40);
		}

		&__logo-wrap {
		}

		&__logo img {
			width: var(--global-nav-logo-mobile-width);
			height: auto;

			@media (--viewport-large) {
				width: var(--global-nav-logo-width);
			}
		}

		&__icons {
			&-wrap {
				margin-left: auto;
				display: flex;
				align-items: center;
				justify-items: center;
				column-gap: var(--spacer-10);

				.icon {
					color: var(--color-text);
					font-size: calc( var(--font-size-body-small) * 2 );
					transition: var(--transition);

					@media (--viewport-large) {
						font-size: var(--font-size-body-large);
					}

					.has-dark-background-color & {
						color: var(--color-neutral-20);
					}

					&.icon-mail--newsletter {
						position: relative;

						&:after {
							content: "";
							display: block;
							position: absolute;
							top: 2px;
							right: 0;
							height: 10px;
							width: 10px;
							background-color: var(--color-secondary);
							border: 2px solid var(--color-neutral-20);
							box-sizing: border-box;
							border-radius: var(--border-radius-circle);
							transition: var(--transition);

							@media (--viewport-large) {
								top: 0;
								height: 8px;
								width: 8px;
							}
						}
					}
				}

				.scroll-up &,
				.scroll-down & {
					right: var(--spacer-30);

					@media (--viewport-large) {
						right: var(--spacer-40);
					}
				}
			}

			&-link {
				display: flex;
				align-items: center;
				justify-items: center;

				button& {
					background: none;
					padding: 0;
					border: unset;
				}

				&:hover,
				&:focus,
				&:active {
					.icon {
						color: var(--color-neutral-50);

						&.icon-mail--newsletter {
							&:after {
								border-color: var(--color-neutral-50);
							}
						}
					}
				}
			}
		}
	}
}

#main-content.hide-nav {
	transition: var(--transition);

	.progress-bar,
	.post-navigation-bar {
		display: none;
	}
}
