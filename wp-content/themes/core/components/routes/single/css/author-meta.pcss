.item-single__author-container {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: var(--spacer-40);

	.item-single__footer & {
		flex-direction: column;
		justify-content: center;
		text-align: center;
		margin-bottom: var(--spacer-70);

		@media (--viewport-medium) {
			flex-direction: row;
			text-align: left;
			margin-bottom: var(--spacer-80);
		}

		&:not(:last-of-type) {
			margin-bottom: var(--spacer-50);
		}

		.item-single__author-image-img {
			border-radius: 50%;
		}
	}

	@media (--viewport-medium) {
		justify-content: flex-start;

		.item-single--media & {
			justify-content: center;
		}

		.item-single--tool & {
			justify-content: center;
		}
	}
}

.item-single__author-infos {
	flex: 1;
}

.item-single__author-image {
	margin-right: var(--spacer-10);
	display: none;

	.item-single__avatars_wrapper & {
		margin-right: var(--spacer-20);

		&:last-child {
			margin-right: var(--spacer-10);
		}
	}

	@media (--viewport-full) {
		display: block;
	}

	.item-single__footer & {
		display: block;
		margin-right: 0;
		margin-bottom: var(--spacer-30);

		@media (--viewport-medium) {
			margin-right: var(--spacer-40);
			margin-bottom: 0;
		}
	}

	.acf-block-preview & {
		margin-right: var(--spacer-20);
		display: inline-flex;
	}

	.item-single__author-image-img {
		width: 30px;
		height: 30px;
		border-radius: 50%;
		background-repeat: no-repeat;
		background-position: center;
		background-size: cover;
		object-fit: cover;

		.item-single--stream & {
			width: 70px;
			height: 70px;
		}

		.item-single__footer & {
			width: 100px;
			height: 100px;

			@media (--viewport-medium) {
				width: 120px;
				height: 120px;
			}
		}
	}
}

.item-single__author-name {
	font-weight: var(--font-weight-regular);
	font-size: var(--font-size-body-small);

	a {
		&:hover,
		&:focus {
			color: var(--color-primary);
			text-decoration: underline;
		}
	}

	@media (--viewport-medium) {
		font-size: var(--font-size-heading-xxsmall);
	}

	.item-single__footer & {
		@mixin t-display-xxx-small;

		margin-bottom: var(--spacer-20);

		@media (--viewport-full) {
			@mixin t-display-x-small;

			margin-bottom: var(--spacer-10);
		}
	}
}

.item-single__author-name-placeholder {
	display: inline-block;

	@media (--viewport-medium) {
		display: none;

		.b-content-loop & {
			display: inline-block;
		}
	}
}

.item-single__author-about {
	@mixin t-body-small;
}

.item-single__author-links {
	@mixin t-label;
	font-size: var(--font-size-body-xsmall);
	margin-top: var(--spacer-10);
	color: var(--color-neutral-30);
	display: flex;
	gap: calc(var(--spacer-10) / 2);
	align-items: center;
	margin-bottom: unset;
	justify-content: center;

	@media (--viewport-medium) {
		justify-content: flex-start;
	}

	.item-single__author-links-label {
		padding-right: var(--spacer-10);
	}
}

a.item-single__author-links-link {
	font-size: 24px;
	line-height: 1;
	text-decoration: none;

	.icon, span {
		color: var(--color-neutral-30);
		transition: var(--transition);
	}

	&--text-icon {
		display: flex;
		align-items: center;
		gap: var(--spacer-10);
	}

	&:hover,
	&:focus,
	&:active {
		text-decoration: none;

		.icon, span {
			color: var(--color-primary);
		}
	}
}

.item-single__author-links-link__text{
	font-size: var(--font-size-body-xsmall);
}

.c-post-info {
	.item-single__post-meta & {
		margin-bottom: var(--spacer-40);
		justify-content: center;
		flex-direction: column;

		@media (--viewport-medium) {
			justify-content: flex-start;
		}

		@media (--viewport-full) {
			flex-direction: row;
		}

		.item-single--post & {
			align-items: center;

			.c-post-info__meta-container {
				gap: 0;
			}

			@media (--viewport-medium) {
				.c-post-info__meta-container,
				.c-post-info__meta-content {
					align-items: flex-start;
				}
			}
		}
	}

	.item-single--media & {
		justify-content: center;
	}

	.c-image.item-single__author-image {
		margin-right: calc(var(--spacer-20) / 2);
	}

	.c-image__image.item-single__author-image-img {
		width: 54px;
		height: 54px;
		object-fit: cover;
	}

	.c-post-info__author-name,
	.c-post-info__divider,
	.c-post-info__publishing-date {
		font-size: var(--font-size-heading-xxsmall);
	}

	.c-post-info__divider {
		display: none;

		@media (--viewport-full) {
			display: block;
		}
	}
}
