.item-single--perfect-listicle-layout {
	&:has(.item-single__main-ad) {
		.l-container {
			position: relative;
			z-index: 1;
		}
	}

    .item-single {
        &__hidden-wrapper {
            height: 0;
            padding: 0;
            margin: 0;
        }

        &__header,
        &__header-shortlist {
            background-color: var(--color-neutral-10);

            .l-container {
                @media (--viewport-full) {
                    padding-left: calc(var(--grid-margin) + var(--grid-margin));
                    padding-right: calc(var(--grid-margin) + var(--grid-margin));
                }

                @media (--viewport-large) {
                    max-width: var(--grid-width-staggered-double);
                    padding-left: 0;
                    padding-right: 0;
                }
            }
        }

        &__header {
			position: relative;
			z-index: 3;

            .item-single__subheader-wrapper {
                margin-bottom: 0;
            }
        }

        &__header-shortlist {
            margin-bottom: calc(var(--spacer-70) + var(--spacer-10));
            padding-bottom: var(--spacer-40);

            .item-single__content-container {
                grid-template-columns: 1fr;

                @media (--viewport-medium) {
                    padding-left: var(--grid-margin);
                    padding-right: var(--grid-margin);
                }

                @media (--viewport-full) {
                    padding-left: 0;
                    padding-right: 0;
                }
            }

            .b-shortlist__item-link {
                letter-spacing: .1px;
                line-height: 1.5;
                color: var(--color-primary);
                text-decoration: underline;
                font-weight: var(--font-weight-semibold);

                &:hover, &:focus, &:active {
                    color: var(--color-link-hover);
                }
            }

            .b-shortlist {
                width: 100%;
            }

            .b-shortlist__container {
                padding-left: 0;
                padding-right: 0;
            }

            .b-shortlist__content {
                max-width: 100%;
            }

            .b-shortlist__featured__content__title {
                color: var(--color-primary);
                font-weight: var(--font-weight-semibold);

                &:hover, &:focus, &:active {
                    color: var(--color-link-hover);
                }
            }
        }

        &__subheader-wrapper {
            background-color: unset;

            @media (--viewport-full) {
                padding: var(--spacer-70) 0 var(--spacer-40);
            }
        }

        &__category-archive-link {
        }

        &__subheader-container {
            align-items: flex-start;
            column-gap: var(--spacer-80);
            flex-direction: column;

            @media (--viewport-full) {
                flex-direction: row;
            }
        }

        &__post-meta {
            display: grid;
            grid-template-columns: 1fr;
            grid-template-rows: auto;
            grid-column-gap: 0;
            grid-row-gap: 0;
            grid-auto-flow: row;
            grid-auto-rows: auto;
            grid-template-areas: "title" "category";

			&:has(.item-single__post-meta__page-subtitle) {
				grid-template-areas: "title" "subtitle" "category";
			}

            @media (--viewport-full) {
                display: flex;
                flex-direction: column;
                padding: 0;
            }

            .page-title {
                @mixin t-display-small;
                margin-bottom: var(--spacer-30);
                text-align: center;
                grid-area: title;
                letter-spacing: -.2px;

                @media (--viewport-full) {
                    @mixin t-display;
                    text-align: left;
                    grid-area: unset;
                }
            }

            &__subtitle {
                display: block;
                margin-bottom: var(--spacer-20);
                color: var(--color-neutral-50);
            }

            &__excerpt,
            &__table-of-contents,
            &__disclaimer,
            &__featured-image {
                &:not(:last-of-type) {
                    margin-bottom: var(--spacer-40);
                }
            }

            &__table-of-contents {
                &__link {
                    color: var(--color-primary-50);
                    font-weight: var(--font-weight-bold);
                }

                &__separator {
                    padding: 0 var(--spacer-10);

                    &::after {
                        @mixin separators-dot;
                        right: 0;
                        top: 0;
                        display: inline-block;
                        position: relative;

                        @media (--viewport-medium) {
                            display: inline-block;
                        }
                    }

                    &:last-of-type {
                        display: none;
                    }
                }
            }

            &__featured-image {
                padding-top: var(--spacer-20);
                padding-bottom: var(--spacer-20);

                .c-image {
                    border-radius: var(--border-radius-media);
                    overflow: hidden;
                }
            }

			&__page-subtitle {
				margin-bottom: var(--spacer-30);
				grid-area: subtitle;
				text-align: center;
				text-wrap: balance;

                @media (--viewport-full) {
					text-align: left;
                    grid-area: unset;
                }
			}

			.page-subtitle {
				@mixin t-display-x-small;
				text-align: center;
				color: var(--color-headings);

				@media (--viewport-full) {
					@mixin t-display-small;
					text-align: left;
				}
			}
        }

        &__post-info-container {
            @media (--viewport-full) {
                margin-bottom: var(--spacer-40);
            }

            .c-post-info__meta-content--disclaimer {
                margin-top: var(--spacer-40);
                color: var(--color-neutral-50);
                font-size: var(--font-size-body-xsmall);
                text-align: center;
                line-height: 1.5;

                @media (--viewport-full) {
                    text-align: left;
                    margin-top: 0;
                }

                a {
                    color: var(--color-primary-50);
                    font-weight: var(--font-weight-semibold);

                    &:hover, &:focus, &:active {
                        color: var(--color-primary-70);
                    }
                }
            }

            & > .c-post-info__meta-content--disclaimer {
                margin-top: var(--spacer-30);
            }
        }

        &__post-info {
            display: flex;
            justify-content: center;
            margin-bottom: var(--spacer-40);
            flex-direction: column;

            @media (--viewport-full) {
                justify-content: flex-start;
                align-items: center;
                margin-bottom: 0;
                flex-direction: row;
                display: grid;
                grid-template-columns: auto auto 1fr;
                grid-gap: var(--spacer-40);
            }

            &--with-badge {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                @media (--viewport-full) {
                    flex-direction: row;
                }
            }

			&__wrapper {
				width: 100%;
				display: flex;

				@media (--viewport-medium-max) {
					position: relative;
				}
			}

            .is-loading {
                display: none;
            }

            .c-post-info {
                margin-bottom: 0;

                &__meta-content--badge {
                    margin-top: var(--spacer-10);

                    @media (--viewport-full) {
                        margin-top: 0;
                        margin-left: var(--spacer-10);
                        user-select: none;
                    }
                }

                &__badge {
                    white-space: nowrap;
                    position: relative;

                    &__title {
                        font-size: var(--font-size-body-xsmall);
                        font-weight: var(--font-weight-semibold);
                        text-transform: uppercase;
                        color: var(--color-alert-positive);
                        border: 1px solid var(--color-alert-positive);
                        border-radius: var(--border-radius-highlights);
                        padding: 0 var(--spacer-10) 0 var(--spacer-30);
                        margin-left: 4px;
                        letter-spacing: 1.2px;
                    }

                    &--tool_expert {
                        &:before {
                            content: url('../../../../../core/assets/img/icons/check-in-green-circle.svg');
                            display: block;
                            width: 22px;
                            height: 22px;
                            position: absolute;
                            top: 2px;
                            left: 0;
                        }
                    }
                }

                &__meta-content--contributors {
                    z-index: 1;

                    & + .c-post-info__meta-content--reviewed-by {
                        margin-top: var(--spacer-10);
                    }

					@media (--viewport-medium-max) {
						position: relative;
					}
                }

                &__reviewed-by,
                &__contributors {
                    color: var(--color-neutral-50);
                    font-size: var(--font-size-special-small);
                    text-align: center;

                    @media (--viewport-full) {
                        text-align: left;
                    }
                }

                &__author-data {
                    display: inline-block;
                }

                &__author-name {
                    cursor: pointer;
                    font-weight: var(--font-weight-semibold);

                    &:hover,
                    &:focus {
                        color: var(--color-neutral-70);
                        text-decoration: underline;
                    }
                }

                &__meta-content-divider {
                    display: none;

                    @media (--viewport-full) {
                        display: block;
                        width: 1px;
                        height: 100%;
                        background-color: var(--color-neutral-20);
                    }
                }

                &__meta-content__entities-container {
                    @media (--viewport-medium-max) {
                        margin-top: var(--spacer-30);
                    }
                }

                &:not(.c-post-info--multiple-authors) {
                    .item-single__author-image {
                        display: none;

                        @media (--viewport-xsmall) {
                            display: block;
                        }
                    }
                }

                .item-single__author-image {
                    &:not(:last-of-type) {
                        margin-right: 0;
                    }
                }

                .c-image__image.item-single__author-image-img {
                    width: var(--spacer-60);
                    height: var(--spacer-60);
                }

                .c-post-info__author-container,
                .c-post-info__author-name,
                .c-post-info__divider,
                .c-post-info__publishing-date {
                    font-size: var(--font-size-body-small);
                }

                .c-post-info__author-name,
                .c-post-info__publishing-date {
                    color: var(--color-text);
                }

                &__meta-content--reviewed-by {
                    position: relative;
                    cursor: default;
                    width: 100%;
                    color: var(--color-neutral-50);
                    margin-top: var(--spacer-20);

                    @media (--viewport-medium-max) {
                        text-align: center;
                    }
                }

                &__meta-content {
					@media (--viewport-full) {
						position: relative;
					}

					&--authors {
						z-index: 3;
					}

					&-contributors {
						z-index: 2;
					}

					&-reviewed-by {
						z-index: 1;
					}

                    .c-tooltip {
                        padding: var(--spacer-30);
                        width: 100%;
                        top: 100%;
                        font-size: var(--font-size-body-small);
                        text-align: left;
                        flex-direction: column;
                        min-height: fit-content;
                        left: 50%;
                        transform: translateX(-50%);

                        &:after {
                            content: none;
                        }

                        @media (--viewport-full) {
                            top: calc(100% - 2px);
                            width: 330px;
                        }

                        h3 {
                            font-weight: var(--font-weight-semibold);
                            margin-bottom: var(--spacer-20);
                            display: inline-block;

                            &:before {
                                content: "";
                                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'%3E%3Cdefs%3E%3Cstyle%3E.a%7Bfill:%23004CFF;%7D.b%7Bfill:%23fff;%7D%3C/style%3E%3C/defs%3E%3Cg transform='translate(-226 -68)'%3E%3Crect class='a' width='20' height='20' rx='10' transform='translate(226 68)'/%3E%3Cg transform='translate(231 73.479)'%3E%3Cpath class='b' d='M10.667,1.262,9.413,0,3.556,5.858,1.262,3.573,0,4.827,3.556,8.373Z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
                                display: inline-block;
                                margin-right: 7px;
                                background-size: 32px 32px;
                                width: 32px;
                                height: 32px;
                                margin-bottom: -9px;
                            }
                        }

                        span {
                            line-height: var(--line-height-heading-xsmall);
                        }

                        &__author-info-wrapper {
                            display: flex;
                            align-items: center;
                            margin-bottom: var(--spacer-30);

                            &--details {
                                display: flex;
                                flex-direction: column;
                            }
                        }

                        &__description {
                            margin-bottom: var(--spacer-30);
                            -webkit-box-orient: vertical;
                            display: -webkit-box;
                            -webkit-line-clamp: 4;
                            overflow: hidden;
                        }

                        .a-cta {
                            width: fit-content;
                        }
                    }
                }

                &__reviewed-by-highlighted {
                    display: flex;
                    color: var(--color-primary);
                    cursor: pointer;
                    float: left;
                    margin-top: 2px;

                    @media (--viewport-full) {
                        position: relative;
                    }

                    &:before {
                        content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'%3E%3Cdefs%3E%3Cstyle%3E.a%7Bfill:%23004CFF;%7D.b%7Bfill:%23fff;%7D%3C/style%3E%3C/defs%3E%3Cg transform='translate(-226 -68)'%3E%3Crect class='a' width='20' height='20' rx='10' transform='translate(226 68)'/%3E%3Cg transform='translate(231 73.479)'%3E%3Cpath class='b' d='M10.667,1.262,9.413,0,3.556,5.858,1.262,3.573,0,4.827,3.556,8.373Z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
                        display: inline-block;
                        margin-right: 7px;
                    }
                }
            }
        }

        &__category-meta {
            justify-content: center;
            grid-area: category;
            margin-bottom: var(--spacer-10);

            @media (--viewport-full) {
                justify-content: flex-start;
                grid-area: unset;
                margin-bottom: var(--spacer-20);
            }
        }

        &__category-link {
			&::after {
				display: none !important;

				@media (--viewport-large) {
					display: block !important;
				}
			}
        }

        &__reading-time {
            display: none;

            @media (--viewport-full) {
                display: inline;
            }
        }

        &__disclaimer {
        }

        &__content-container {
            display: grid;
            grid-template-columns: 1fr;
            grid-template-rows: auto;
            grid-column-gap: 0;
            grid-row-gap: 0;
            grid-auto-flow: row;
            grid-auto-rows: auto;
            justify-items: center;

            @media (--viewport-large) {
                padding: 0;
                grid-auto-flow: column;
                grid-template-columns: 1fr var(--grid-sidebar-col);
                grid-column-gap: var(--spacer-80);
                justify-items: stretch;
            }
        }

		&__floating-container {
			margin: var(--spacer-70) auto var(--spacer-70);

			@media (--viewport-large) {
				margin: 0 auto;
			}

			.c-ad-block-inline {
				margin-top: 0;
			}

			&__close-button {
				display: none;
				position: absolute;
				top: 0;
				right: 0;
				z-index: 1;

				&:before {
					background-color: var(--color-background-light);
					border-radius: var(--border-radius-circle);
					color: var(--color-black);
					content: var(--icon-close);
					font-family: var(--font-family-core-icons);
					font-size: var(--font-size-body-small);
					line-height: 1;
					text-align: center;
					transition: var(--transition);
					padding: calc(var(--spacer-10) / 2);
				}
			}
		}

        &__footer {
            margin-top: var(--spacer-70);
            padding-top: var(--spacer-50);
            padding-bottom: var(--spacer-50);
            border-top: 1px solid var(--color-neutral-20);

            .item-single__author-name {
                margin-bottom: var(--spacer-20);
            }

            .item-single__author-links {
                margin-top: var(--spacer-20);
            }
        }

		&__main-ad {
			position: relative;
			z-index: 2;

			@media (--viewport-large) {
				margin-bottom: var(--spacer-40);
			}
		}
    }

    .b-interstitial {
        .b-interstitial__content {
            padding: var(--spacer-60);
        }

        &.c-block--layout-left,
        &.c-block--layout-right {
            @media (--viewport-medium) {
                .b-interstitial__content {
                    flex: 0 0 65%;
                    width: 65%;
                }

                .b-interstitial__media {
                    flex: 0 0 35%;
                    width: 35%;
                }
            }
        }
    }
}

/*Overflowing rules to not use important*/
#main-content .item-single--perfect-listicle-layout {
    .item-single {
        &__post-info {
            .c-post-info,
            .c-post-info__meta-container {
                align-items: center;

                @media (--viewport-full) {
                    align-items: flex-start;
                }
            }

            .c-post-info {
                flex-direction: row;
                align-items: center;
                margin: 0 auto;

                @media (--viewport-full) {
                    flex-direction: row;
                    margin: unset;
                }

                &__publishing-date {
                    font-size: var(--font-size-special-small);

                    @media (--viewport-full) {
                        font-size: var(--font-size-body-small);
                    }
                }
            }

            .c-post-info__meta-container {
                gap: 0;

                @media (--viewport-full) {
                    gap: var(--spacer-10);
                }
            }
        }

        &__sidebar {
            .c-ad-block-inline {
                margin-top: 0;
            }

            .c-ad-block-inline .c-ad-block__logo-img {
                max-width: 220px;
            }

            .c-ad-block--layout-inline .c-ad-block__description {
                @media (--viewport-full) {
                    padding-left: var(--spacer-20);
                    padding-right: var(--spacer-20);
                }
            }
        }

        &__content {
            .c-block {
                margin-top: var(--spacer-60);
                margin-bottom: var(--spacer-60);

                @media (--viewport-medium) {
                    margin-top: var(--spacer-70);
                    margin-bottom: var(--spacer-70);
                }

                &:first-child {
                    margin-top: 0;
                }
            }

            &.l-sink {
                > * {
                    @media (--viewport-large) {
                        width: 100%;
                        max-width: 100%;
                    }
                }

                .l-container {
                    @media (--viewport-large) {
                        padding-right: 0;
                        padding-left: 0;
                    }
                }
            }

            .c-ad-block-inline {
                max-width: var(--grid-width-staggered-double);

                .c-ad-block__logo img {
                    max-height: 80px;
                }
            }
        }

        &__author-container {
            margin: 0;
            width: 100%;

            @media (--viewport-medium) {
                align-items: flex-start;
            }
        }

        &__footer {
            .item-single__author-container {
                &:not(:first-of-type) {
                    margin-top: var(--spacer-50);
                }
            }
        }
    }
}
