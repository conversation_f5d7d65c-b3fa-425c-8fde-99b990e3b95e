.item-single {
	&.item-single--stream {
		> .t-sink {
			> :last-child:is(.c-block, p, .wp-block-image, .wp-block-embed, img) {
				padding-bottom: 80px;

				@media (--viewport-medium) {
					padding-bottom: 120px;
				}
			}
		}
	}

	&--post {
		.c-post-info {
			&--multiple-authors {
				align-items: flex-start;

				@media (--viewport-xsmall-max) {
					flex-direction: column !important;
					align-items: center;
				}

				.item-single__author-image {
					&:not(:first-child) {
						margin-left: -15px;
					}
				}

				.c-post-info__meta-container {
					@media (--viewport-small) {
						align-items: flex-start;
					}

					.c-post-info__meta-content {
						@media (--viewport-xsmall-max) {
							text-align: center;
						}
					}
				}
			}
		}
	}
}

.item-single__subheader-wrapper {
	background-color: var(--color-neutral-10);
	padding: var(--spacer-50) 0 var(--spacer-40);
	margin-bottom: var(--spacer-50);

	@media (--viewport-full) {
		padding: var(--spacer-30) 0 var(--spacer-70);
	}

	.item-single--media & {
		position: relative;

		@media (--viewport-full) {
			padding-bottom: 0;
		}

		&.has-background-image {
			background-color: transparent;
			color: var(--color-white);

			@media (--viewport-large) {
				padding-top: 0;
				height: 90vh;
			}

			a {
				color: var(--color-white);

				&:hover, &:focus, &:active {
					color: var(--color-primary);
				}

				&.social-share-networks__anchor {
					&:hover, &:focus, &:active {
						color: var(--color-neutral-30);
					}
				}
			}

			&::before {
				content: '';
				position: absolute;
				top: 0;
				right: 0;
				bottom: 0;
				left: 0;
				background-color: var(--color-background-dark);
				opacity: 0.5;
				z-index: -5;
			}

			.l-container {
				@media (--viewport-large) {
					height: 100%;
					display: flex;
					flex-direction: column;
					padding-bottom: var(--spacer-70);
					justify-content: flex-end;
				}
			}

			&:has(.item-single__subheader-background) {
				margin-bottom: var(--spacer-80);

				@media (--viewport-large) {
					margin-bottom: var(--spacer-50);
				}
			}

			.item-single__embed-container {
				margin: 0 auto calc( var(--spacer-60) * -1 );

				@media (--viewport-large) {
					margin: 0 auto var(--spacer-30);
				}
			}
		}

		.item-single__subheader-background {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			background-size: cover;
			background-position: center;
			background-repeat: no-repeat;
			z-index: -10;
		}
	}

	/* Remove bottom padding from Tool CPT since we will show ads in subheader here */

	.item-single--tool & {
		padding: var(--spacer-50) 0;
		margin-bottom: 0;

		@media (--viewport-full) {
			padding: var(--spacer-30) 0 120px;
		}
	}

	.item-single--stream.post-state-on-demand.logged-out &,
	.item-single--stream.post-state-upcoming.logged-out & {
		margin-bottom: 0;
	}

	.item-single--stream.post-state-on-demand.logged-out & {
		padding: var(--spacer-50) 0;

		@media (--viewport-full) {
			padding: var(--spacer-80);
		}
	}

	.item-single--stream.post-state-on-demand.logged-in & {
		padding-top: var(--spacer-20);
		padding-bottom: 0;
	}

	/* Accounting for the membership nav */

	.has-membership-menu .item-single--stream.post-state-on-demand.logged-in & {
		padding-top: var(--spacer-50);
	}

	.item-single--media & {
		padding-top: var(--spacer-20);
		padding-bottom: 0;
	}

	/* CASE: Post has no category and therefore no breadcrumbs above main content */

	> .l-container:first-child {
		@media (--viewport-full) {
			margin-top: calc(var(--spacer-80) - var(--spacer-30));
		}
	}

	/* CASE: Download Single - Logged out state */

	.item-single--download.logged-out & {
		margin-bottom: 0;
	}
}

.item-single__subheader-container {
	display: flex;
	align-items: center;
	flex-direction: column-reverse;

	@media (--viewport-full) {
		flex-direction: row;
	}

	/* CASE: Member Stream - Upcoming || Download Template */

	.item-single--download &,
	.item-single--stream.post-state-upcoming & {
		flex-direction: column;

		@media (--viewport-medium) {
			flex-direction: row;
		}
	}

	.item-single__featured-image {
		position: relative;
		max-width: var(--grid-5-col);
		width: 100%;
		margin-top: var(--spacer-30);

		@media (--viewport-full) {
			margin-top: 0;
		}

		.c-image {
			position: relative;

			&::before {
				display: block;
				content: '';
				width: 100%;
				padding-bottom: calc(100% / 1);
			}

			/*Todo: Evolve to use aspect-ratio: 1 / 1 in the future*/

			.c-image__image {
				position: absolute;
				top: 0;
				right: 0;
				bottom: 0;
				left: 0;
				width: 100%;
				height: 100%;
				object-fit: cover;
				background-size: cover;
				background-repeat: no-repeat;
				background-position: center;
				border-radius: var(--border-radius-media);
			}
		}

		/* CASE: Member Stream - Upcoming Template */

		.item-single--stream.post-state-upcoming & {
			max-width: var(--grid-6-col);
			margin-bottom: var(--spacer-40);

			@media (--viewport-medium) {
				flex: 0 1 var(--grid-6-col);
				margin-bottom: 0;
			}

			@media (--viewport-large) {
				flex: 0 0 var(--grid-6-col);
			}
		}

		/* CASE: Download Single Logged in */

		.item-single--download.logged-in & {
			.item-single__featured-image-container {
				cursor: pointer;

				&:hover,
				&:focus {
					.item-single_play-video-button {
						box-shadow: var(--box-shadow-20);
					}
				}
			}
		}
	}

	/* CASE: Member Stream - Countdown overrides from block */

	.c-countdown {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		display: flex;
		justify-content: center;
		align-items: center;

		.c-countdown__timer-container {
			padding: var(--spacer-30) 12px;

			@media (--viewport-large) {
				padding: var(--spacer-40);
			}
		}

		.c-countdown__item:not(:last-child) {
			margin-right: 10px;

			@media (--viewport-large) {
				margin-right: 20px;
			}
		}

		.c-countdown__item .c-countdown__unit {
			@mixin t-display-x-small;

			&:before {
				content: ":";
				position: absolute;
				right: -8px;
				top: 50%;
				transform: translateY(-50%);
				color: var(--color-black);

				@mixin t-display-x-small;

				@media (--viewport-large) {
					right: -13px;

					@mixin t-display-small;
				}
			}

			@media (--viewport-large) {
				@mixin t-display-small;

				font-size: 28px;
			}
		}

		.c-countdown__item:last-child {
			.c-countdown__unit:before {
				display: none;
			}
		}

		.c-countdown__digit {
			background-color: var(--color-neutral-20);
			border-radius: var(--border-radius-base);
			padding: 0;
			width: 30px;
			height: 42px;
			text-align: center;
			overflow: hidden;

			@media (--viewport-large) {
				padding: 0 var(--spacer-10);
				width: 39px;
				height: 56px;
			}
		}

		.number {
			position: relative;
			line-height: 42px;

			@media (--viewport-large) {
				line-height: 54px;
			}
		}
	}
}

.item-single__seats-left {
	display: flex;
	justify-content: center;

	@media (--viewport-medium) {
		justify-content: flex-start;
	}
}

.item-single__video-placeholder {
	max-width: var(--grid-10-col);
	margin: auto;
	position: relative;

	.c-image__image {
		border-radius: 0;

		@media (--viewport-full) {
			border-radius: var(--border-radius-media);
		}
	}
}

.item-single__video-placeholder-overlay {
	@mixin p-fill;

	border-radius: 0;
	background-color: rgba(0, 12, 42, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;

	@media (--viewport-full) {
		border-radius: var(--border-radius-media);
	}
}

.item-single__lock-icon-container {
	width: 90px;
	height: 90px;
	background-color: var(--color-dpm-dark-blue);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: var(--color-white);
	font-size: 64px;

	@media (--viewport-medium) {
		width: 154px;
		height: 154px;
		font-size: 64px;
	}
}

.item-single__member-cta-wrapper {
	background-color: var(--color-dpm-dark-blue);
	color: var(--color-white);

	/* CASE: Download Single - Logged out state */

	.item-single--download.logged-out & {
		margin-bottom: var(--spacer-50);

		@media (--viewport-full) {
			margin-bottom: 80px;
		}
	}
}

.item-single__member-cta-container {
	padding: var(--spacer-40) 0;

	@media (--viewport-medium) {
		padding: var(--spacer-20) 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.item-single__member-cta-actions {
		display: flex;
		flex-direction: column;
		gap: var(--spacer-10);

		@media (--viewport-small) {
			flex-direction: row;
		}
	}

	.item-single__member-cta-button {
		width: 100%;

		@media (--viewport-medium) {
			width: auto;
		}
	}
}

.item-single__member-cta-text {
	font-size: var(--font-size-body);
	display: inline-flex;
	margin-bottom: var(--spacer-30);

	@media (--viewport-medium) {
		align-items: center;
		margin-bottom: 0;
		padding-right: var(--spacer-40);
	}

	.icon {
		font-size: var(--font-size-body-large);
		margin-right: var(--spacer-10);
	}
}

.item-single__post-meta {
	width: 100%;

	@media (--viewport-medium) {
		padding: 0 var(--spacer-50);
	}

	/* CASE: Member Stream - Upcoming Template */

	.item-single--stream.post-state-upcoming & {
		@media (--viewport-medium) {
			flex: 0 1 auto;
		}
	}

	/* CASE: Download Single */

	.item-single--download & {
		padding-left: 0;

		@media (--viewport-large) {
			flex: 0 0 var(--grid-6-col);
		}
	}

	.page-title {
		font-size: var(--font-size-heading-small);
		margin-bottom: var(--spacer-30);
		text-align: center;

		@media (--viewport-medium) {
			text-align: left;
			font-size: var(--font-size-heading-xlarge);

			.item-single--media & {
				text-align: center;
			}

			.item-single--tool & {
				text-align: center;
				max-width: var(--grid-10-col);
				margin: auto;
				margin-bottom: var(--spacer-30);
			}
		}

		/* CASE: Member Stream - Upcoming Template */

		.item-single--stream.post-state-upcoming & {
			@mixin t-display-small;

			@media (--viewport-medium) {
				@mixin t-display-large;
			}
		}

		.item-single--media .has-background-image & {
			color: var(--color-white);
		}
	}

	.page-excerpt {
		max-width: var(--grid-8-col);
		margin: var(--spacer-30) auto;
		text-align: center;

		.item-single--tool & {
			text-align: center;
			max-width: var(--grid-8-col);

			> p {
				@mixin t-body-small;
			}
		}
	}
}

.item-single__disclaimer {
	@mixin t-caption;
	text-align: center;

	@media (--viewport-medium) {
		text-align: left;
	}

	.item-single--media &,
	.item-single--tool & {
		max-width: var(--grid-6-col);
		display: flex;
		text-align: center;
		margin: 0 auto var(--spacer-50) auto;
	}

	.item-single--editorial & {
		text-align: left;
	}
}

.item-single__content-wrapper {
	display: flex;
	flex-direction: column;
	padding: var(--spacer-50) 0;

	@media (--viewport-full) {
		flex-direction: row;
		padding: 80px 0;
	}

	.item-single--stream.logged-in & {
		padding-top: var(--spacer-20);

		@media (--viewport-full) {
			padding-top: var(--spacer-50);
		}
	}

	/* CASE: Member Stream - Upcoming */

	.item-single--stream.post-state-upcoming & {
		flex-direction: column-reverse;

		@media (--viewport-full) {
			flex-direction: row;
		}
	}

	/* CASE: Member Stream - Upcoming Logged Out */

	.item-single--stream.post-state-upcoming.logged-out & {
		@media (--viewport-full) {
			justify-content: center;
		}
	}
}

.item-single__stream-details {
	padding-right: 0;

	@media (--viewport-full) {
		flex: 1;
		padding-right: var(--spacer-80);
	}

	/* CASE: Member Stream - Upcoming Logged In */

	.item-single--stream.post-state-upcoming & {
		@media (--viewport-full) {
			flex: 0 1 auto;
		}
	}

	/* CASE: Member Stream - Upcoming Logged Out */

	.item-single--stream.post-state-upcoming.logged-out & {
		@media (--viewport-full) {
			flex: 0 1 var(--grid-7-col);
			padding-right: 0;
		}
	}

	.page-title {
		text-align: center;

		@media (--viewport-medium) {
			text-align: left;
		}
	}

	ul {
		font-size: var(--font-size-body-small);
		list-style: disc outside;
		padding-left: 1.5em;

		@media (--viewport-full) {
			font-size: var(--font-size-body);
		}

		li ~ li {
			margin-top: 0;

			@media (--viewport-full) {
				font-size: var(--font-size-body);
			}
		}
	}
}

.item-single__content {
	margin-bottom: var(--spacer-80);

	.wp-block-group:is(:last-child) {
		margin-bottom: 0;
	}

	/* CASE: Remove margin from first custom block on tool page */

	.item-single--tool & {

		> :first-child:not(.c-listicle):not(.wp-block-group) {
			margin-top: 80px;

			@media (--viewport-medium) {
				margin-top: 120px;
			}
		}
	}

	/* CASE: Member Stream */

	.item-single--stream & {
		margin: var(--spacer-40) 0;

		ul {
			margin: var(--spacer-30) 0 var(--spacer-40);

			li {
				&::marker {
					color: var(--color-primary);
				}
			}
		}
	}

	/* CASE: Member Stream - Upcoming */

	.item-single--stream.post-state-upcoming & {
		margin-top: 0;
	}

	/* CASE: Download */

	.item-single--download & {
		display: none;
		margin-bottom: var(--spacer-30);

		p {
			@mixin t-body-small;
		}

		@media (--viewport-medium) {
			display: block;
		}
	}
}

.item-single__date {
	@mixin t-label;

	display: flex;
	align-items: center;
	margin-top: var(--spacer-20);
	font-weight: var(--font-weight-bold);

	.icon {
		color: var(--color-primary);
		margin-right: var(--spacer-10);
		font-size: var(--font-size-heading-xxsmall);
	}

	/* CASE: Member Stream - All templates */

	.item-single__stream-details &,
	.item-single--stream.post-state-upcoming & {
		justify-content: center;

		@media (--viewport-medium) {
			justify-content: flex-start;
			flex-flow: row nowrap;
		}
	}

	.item-single--stream.post-state-upcoming & {
		flex-flow: column nowrap;

		@media (--viewport-medium) {
			flex-flow: row nowrap;
		}
	}
}

.item-single__date-day,
.item-single__date-time {
	flex: 1 1 100%;

	@media (--viewport-medium) {
		flex: 0 1 auto;
	}
}

.item-single__date-day {
	margin-bottom: var(--spacer-20);

	@media (--viewport-medium) {
		margin-right: var(--spacer-20);
		margin-bottom: 0;
	}
}

.item-single__share {
	display: flex;
	align-items: center;
	margin-top: var(--spacer-20);

	/* CASE: Member Stream - All templates */

	.item-single__stream-details &,
	.item-single--stream.post-state-upcoming & {
		justify-content: center;

		@media (--viewport-medium) {
			justify-content: flex-start;
		}
	}

	/* CASE: Download Single */

	.item-single--download & {
		justify-content: center;
		margin-top: var(--spacer-30);

		@media (--viewport-medium) {
			justify-content: flex-start;
		}
	}

	.social-share {
		margin-bottom: 0;
	}
}

.item-single__share-title {
	@mixin t-label;

	font-weight: var(--font-weight-bold);
	margin-right: var(--spacer-10);
}

.item-single__speakers-container {
	/* CASE: Member Stream - On Demand Logged In */

	.item-single--stream.post-state-on-demand & {
		@media (--viewport-full) {
			flex: 0 0 var(--grid-4-col);
			max-width: var(--grid-4-col);
		}
	}

	.item-single__speakers-title {
		@mixin t-overline;

		margin-bottom: var(--spacer-20);
	}

	.item-single__author-container {
		align-items: flex-start;
		flex-flow: row wrap;
	}

	.item-single__author-image {
		max-width: 100%;
		flex: 0 0 70px;
		width: 70px;
		height: 70px;
		margin-bottom: var(--spacer-20);
		margin-right: 0;
		display: block;
	}

	.item-single__author-infos {
		flex: 0 1 calc(100% - 70px);
		padding-left: var(--spacer-20);

		&:first-child {
			flex: 0 0 100%;
			padding-left: 0;
			margin-bottom: var(--spacer-20);
		}
	}

	.item-single__author-name {
		&:not(:last-child) {
			margin-bottom: var(--spacer-10);
		}
	}

	.item-single__author-job-title {
		@mixin t-label;
	}

	.item-single__author-about {
		@mixin t-body-small;

		flex: 0 0 100%;
	}
}

/* Member Stream - Upcoming Event Big Marker Form */
.item-single__stream-form-wrapper {
	display: flex;
	flex-direction: column;
	gap: var(--spacer-40);

	@media (--viewport-full) {
		flex: 0 0 var(--grid-4-col);
	}

	&--embed {
	}

	&--gravity-form {
		.gform_wrapper {
			width: 100%;
		}
	}
}

.item-single__embed-container {
	margin: 0 auto var(--spacer-30) auto;
	max-width: var(--grid-10-col);

	@media (--viewport-full) {
		margin-bottom: var(--spacer-70);
	}

	.item-single--stream.logged-in & {
		margin-left: calc(-1 * var(--grid-margin-small));
		margin-right: calc(-1 * var(--grid-margin-small));

		@media (--viewport-full) {
			margin-left: auto;
			margin-right: auto;
		}
	}

	iframe {
		width: 100%;
		max-width: 100%;
		max-height: calc((100vw - 40px) / (16 / 9));
	}
}

.item-single__embed-container--video {
	iframe {
		height: 562px;
		border-radius: var(--border-radius-media);

		.item-single--stream & {
			filter: drop-shadow(0 4px 40px rgba(21, 21, 21, 0.08));

			@media (--viewport-medium) {
				border-radius: var(--border-radius-media);
			}
		}
	}
}

.item-single__embed-container--podcast {
	iframe {
		height: 118px;

		@media (--viewport-full) {
			height: 200px;
		}
	}
}

.item-single__transcript-container {
	margin: 0 auto var(--spacer-80) auto;
	max-width: var(--grid-12-col);
	line-height: 1.5;
	padding: 0 var(--grid-margin-small);

	@media (--viewport-full) {
		font-size: var(--font-size-body);
		line-height: 1.75;
	}

	/* Clearing up some fancy styles coming from Descript export */

	* {
		all: unset;
		background-color: transparent !important;
	}

	p {
		display: block;
		margin-bottom: var(--spacer-20);

		&:not(:has(em)) {
			@media (--viewport-medium) {
				padding-left: calc(var(--grid-3-col) - var(--spacer-70));
				position: relative;
			}

			@media (--viewport-full) {
				padding-left: var(--grid-3-col);
			}
		}

		> :first-child:is(strong) {
			display: block;

			@media (--viewport-medium) {
				position: absolute;
				left: 0;
				top: 0;
				width: calc(var(--grid-3-col) - var(--spacer-80));
				text-align: right;
			}

			@media (--viewport-full) {
				width: calc(var(--grid-3-col) - var(--spacer-50));
			}
		}
	}

	strong {
		font-weight: var(--font-weight-bold);
		margin-bottom: var(--spacer-20);
	}
}

.item-single__footer {
	/* CASE: Download Single */

	.item-single--download & {
		margin-top: var(--spacer-80);
	}

	.item-single--media & {
		margin-top: 80px;

		@media (--viewport-full) {
			margin-top: 120px;
		}
	}
}

/* CASE: Download Single */

.item-single--download {
	.item-single__download-authors {
		width: 100%;
		display: grid;
		gap: var(--spacer-60);
		align-items: start;
		margin-top: var(--spacer-40);
		border-radius: var(--border-radius-media);

		@media (--viewport-medium) {
			& {
				grid-template-columns: 2fr 1fr;
			}
		}

		.item-single__author-type {
			@mixin t-overline;

			margin-bottom: var(--spacer-20);
		}

		.item-single__author-container {
			align-items: flex-start;
			flex-direction: column;
			box-shadow: var(--box-shadow-20);
			padding: var(--spacer-50) calc( var(--spacer-70) - 4px );
			background-color: var(--color-white);
			margin-bottom: 0;
			border-radius: var(--border-radius-media);

			&--expanded .item-single__author-about {
				overflow: initial;
				display: initial;
				-webkit-box-orient: initial;
				-webkit-line-clamp: initial;
			}

			&--expandable .item-single__author-actions__see-more {
				display: block;
			}
		}

		.item-single__author-image {
			max-width: 100%;
			flex: 0 0 var(--spacer-80);
			width: var(--spacer-80);
			height: var(--spacer-80);
			margin-right: 0;
			display: block;

			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}

		.item-single__author-infos {
			display: flex;
			gap: var(--spacer-20);
			width: 100%;
			flex: 0;
			margin-bottom: var(--spacer-20);
		}

		.item-single__author-name {
			&:not(:last-child) {
				margin-bottom: var(--spacer-10);
			}
		}

		.item-single__author-job-title {
			@mixin t-label;
		}

		.item-single__author-about {
			@mixin t-body-small;
			overflow: hidden;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 3;
		}

		.item-single__author-links {
			flex-direction: column;
			align-items: flex-start;
			gap: 0;

			&-link {
				.icon, span {
					color: var(--color-text);
				}
			}
		}

		.item-single__author-actions {
			display: flex;
			align-items: center;
			gap: var(--spacer-20);
			margin-top: var(--spacer-20);

			&__action:first-child:not(:only-child) {
				padding-right: var(--spacer-20);
				border-right: solid 1px var(--color-grey);
			}

			&__see-more {
				display: none;
			}
		}
	}
}

/*
 * Video Watched Section
 */

.item-single__video-progress {
	margin-bottom: var(--spacer-50);
}

.watched-button {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 264px;
	margin-top: var(--spacer-20);
	box-shadow: none;
	border: 2px solid var(--color-primary);

	&.watched {
		background: var(--color-white);
		color: var(--color-primary);
	}
}

.watched-button__icon {
	width: 24px;
	height: 24px;
	background: var(--color-white);
	border-radius: 50%;
	margin-left: var(--spacer-10);
	background-image: url(/wp-content/themes/core/assets/img/icons/eye.svg);
	background-position: center;
	background-repeat: no-repeat;

	.watched & {
		background: var(--color-primary);
		background-image: url(/wp-content/themes/core/assets/img/icons/check-white.svg);
	}
}

/*
 * Variations
 */

.item-single--new-header {
	@media (--viewport-full) {
		& .item-single__subheader-wrapper {
			display: grid;
			grid-template-columns: minmax(auto, calc(808px - var(--grid-margin))) calc(400px + var(--grid-margin));
			justify-content: center;
			padding-top: 80px !important;
			padding-bottom: 85px !important;
			row-gap: 80px !important;
			column-gap: 80px !important;

			& .c-ad-block-inline {
				margin-top: 0 !important;

				.l-container {
					padding: 0 var(--grid-margin) 0 0 !important;

					& .c-ad-block {
						display: flex;
						align-items: center;
						height: 332px !important;
					}

					& .c-ad-block__logo {
						max-width: 250px !important;

						img {
							max-height: 100px;
						}
					}
				}
			}

			& .c-ad-block__container {
				flex-direction: column;
				row-gap: 25px;
				column-gap: 25px;
			}

			& .c-ad-block__description {
				text-align: center;
				flex-grow: 0;
				flex-shrink: 1;
				flex-basis: 0;
			}

			& .c-ad-block__cta {
				flex-grow: 0;
				flex-shrink: 1;
				flex-basis: 0;
				margin-top: 4px;
			}

			& > .l-container {
				padding: 0 0 0 var(--grid-margin) !important;
				margin-top: 0 !important;
			}

			& .item-single__post-meta {
				padding: 0;

				& .item-single__category-meta {
					justify-content: flex-start;
				}

				& .page-title {
					text-align: left;
					font-size: var(--font-size-heading-large);
					font-weight: var(--font-weight-semibold);
				}

				& .page-introduction {
					text-align: left;
				}

				& .c-post-info {
					justify-content: flex-start;
				}
			}
		}
	}
}

.item-single--with-disclaimer {
	@media (--viewport-full) {
		& .item-single__subheader-wrapper {
			grid-template-columns: 1fr !important;
			padding: var(--spacer-70) 0 var(--spacer-70) !important;
		}

		& .item-single__subheader-wrapper > .l-container {
			padding: 0 !important;
			margin: 0 auto !important;
			max-width: 830px;
		}

		& .item-single__subheader-wrapper .item-single__post-meta .page-title {
			text-align: center !important;
			font-size: var(--font-size-heading);
			line-height: var(--line-height-heading);
			font-weight: var(--font-weight-semibold);
		}
	}

	& .item-single__subheader-wrapper .item-single__post-meta .c-post-info {
		margin: 0;
		flex-direction: column;

		@media (--viewport-full) {
			flex-direction: row;
		}

		& .c-post-info__divider {
			display: none;

			@media (--viewport-medium) {
				display: inline-block;
			}
		}

		&__publishing-date-container {
			flex-direction: column;

			@media (--viewport-small) {
				flex-direction: row;
			}

			& .c-post-info__divider {
				display: none;

				@media (--viewport-small) {
					display: block;
				}
			}
		}
	}

	& .c-post-info .c-image.item-single__author-image {
		margin-right: 0;
	}

	& .c-post-info .c-image__image.item-single__author-image-img {
		width: var(--spacer-30) !important;
		height: var(--spacer-30) !important;
	}

	& .item-single__category-meta {
		text-transform: unset;
		margin-bottom: var(--spacer-30);
	}

	& .item-single__category-link {
		text-transform: uppercase;
	}

	& .c-post-info .c-post-info__author-name, .c-post-info .c-post-info__publishing-date {
		font-size: 14px;
		letter-spacing: 0;
	}

	& .c-post-info .c-post-info__author-name {
		font-weight: var(--font-weight-semibold) !important;
	}

	& .c-post-info .c-post-info__publishing-date {
		font-weight: var(--font-weight-medium);

		&--modified {
			font-weight: var(--font-weight-regular);
		}
	}

	& .c-post-info__divider {
		color: var(--color-secondary-50) !important;
		font-size: 16px !important;
	}

	& .item-single__subheader-wrapper .item-single__post-meta .item-single__meta-info {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		margin-bottom: var(--spacer-30);
		flex-direction: column;
		align-items: center;
		gap: 4px;

		@media (--viewport-full) {
			flex-direction: row;
			gap: 0;
		}

		& .item-single__category-link {
			margin-right: 0;
			text-align: center;

			@media (--viewport-full) {
				margin-right: var(--spacer-30);
			}

			&::after {
				display: none;

				@media (--viewport-full) {
					display: block;
				}
			}
		}
	}

	& .item-single__header-disclaimer-wrapper {
		max-width: 620px;
		font-size: 14px;
		font-style: italic;
		color: var(--color-neutral-50);
		text-align: center;
		margin: var(--spacer-30) auto 0;

		a {
			color: var(--color-primary-70);
			font-weight: var(--font-weight-semibold);
		}

		p + p {
			margin-top: var(--spacer-20);
		}
	}
}

.item-single--no-border-listicle {
	.c-block__cta-container--footer {
		margin-top: var(--spacer-40);

		.a-btn {
			width: 100%;
		}
	}

	.item-single--listicle .item-single__subheader-wrapper {
		padding-top: calc(var(--spacer-70) + var(--spacer-10));
		padding-bottom: var(--spacer-70);
	}

	.c-block__header.c-block__header--cta-end {
		margin-bottom: var(--spacer-40);
	}

	h2.b-shortlist__title {
		font-size: var(--font-size-heading-small);
		font-weight: var(--font-weight-semibold);
	}

	.c-content-block__cta {
		margin-top: 0;
	}

	.b-interstitial .c-content-block__cta {
		margin-top: var(--spacer-40);
	}

	.c-block__cta-container--footer .a-btn {
		width: auto;
	}
}
