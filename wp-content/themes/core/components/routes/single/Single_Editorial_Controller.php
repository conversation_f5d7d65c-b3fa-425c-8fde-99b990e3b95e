<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\routes\single;

use Tribe\Project\Object_Meta\Post_Meta;
use Tribe\Project\Object_Meta\Post_Template_Editorial_Meta;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\author\Author_Controller;
use Tribe\Project\Object_Meta\User_Meta;
use Tribe\Project\Templates\Components\Traits\Authors_Links;
use Tribe\Project\Templates\Components\Traits\Handles_MemberPress_Permissions;
use Tribe\Project\Templates\Components\Traits\Handles_Enclosing_Shortcodes;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\Traits\With_Lottie_Data;
use Tribe\Project\Theme\Config\Image_Sizes;

class Single_Editorial_Controller extends Single_Controller {
	use Authors_Links;
	use Handles_MemberPress_Permissions;
	use Handles_Enclosing_Shortcodes;
	use With_Lottie_Data;

	public function __construct( array $args = [] ) {
		$lottie = $this->get_field( Post_Template_Editorial_Meta::LOTTIE );

		if ( ! empty( $lottie ) && ! empty( $lottie['lottie_json_url'] ) ) {
			$this->fill_lottie_data( [
				self::LOTTIE_JSON_URL => $this->get_field( Post_Template_Editorial_Meta::LOTTIE )['lottie_json_url'],
				self::LOTTIE_DATA     => $this->get_field( Post_Template_Editorial_Meta::LOTTIE ),
			] );
		}
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::LOTTIE_DATA     => [],
			self::LOTTIE_JSON_URL => '',
		];
	}

	/**
	 * @return Deferred_Component
	 */
	public function get_header_cta(): Deferred_Component {
		$url    = '#content';
		$label  = __( 'Read Now', 'tribe' );
		$target = '_self';

		if ( $cta = $this->get_field( Post_Template_Editorial_Meta::HEADER_CTA ) ) {
			$url    = $cta['url'];
			$label  = $cta['title'];
			$target = $cta['target'];
		}

		return defer_template_part( 'components/link/link', null, [
			Link_Controller::URL     => esc_url( $url ),
			Link_Controller::CONTENT => $label,
			Link_Controller::TARGET  => $target,
			Link_Controller::CLASSES => [
				'c-block__cta-link',
				'a-btn-secondary',
			],
		] );
	}

	/**
	 * @return bool|null
	 */
	public function hide_navigation(): ?bool {
		return (bool) $this->get_field( Post_Template_Editorial_Meta::HIDE_NAVIGATION ) ?? false;
	}

	/**
	 * @return bool
	 */
	public function hide_quick_summary(): bool {
		return (bool) $this->get_field( Post_Meta::HIDE_QUICK_SUMMARY ) ?? false;
	}

	/**
	 * @return bool
	 */
	public function get_header_has_dark_background(): bool {
		return (bool) $this->get_field( Post_Template_Editorial_Meta::HEADER_DARK_BACKGROUND ) ?? false;
	}

	/**
	 * @return string|null
	 */
	public function get_newsletter_link(): ?string {
		$newsletter_url = get_field( Post_Template_Editorial_Meta::NEWSLETTER_URL, get_the_ID() );

		if ( ! $newsletter_url ) {
			return null;
		}

		return sanitize_url( $newsletter_url );
	}

	/**
	 * @return string
	 */
	public function get_media_type(): string {
		$media_type = $this->get_field( Post_Template_Editorial_Meta::MEDIA_TYPE );

		if ( ! $media_type ) {
			return Post_Template_Editorial_Meta::IMAGE;
		}

		return $media_type;
	}

	/**
	 * @return string
	 */
	private function get_header_lottie_mobile_position(): string {
		$lottie_mobile_position = $this->get_field( Post_Template_Editorial_Meta::HEADER_LOTTIE_MOBILE_BACKGROUND_POSITION );

		if ( ! $lottie_mobile_position ) {
			return Post_Template_Editorial_Meta::HEADER_LOTTIE_BACKGROUND_POSITION_CENTER;
		}

		return $lottie_mobile_position;
	}

	/**
	 * @param $position
	 *
	 * @return bool
	 */
	public function is_header_lottie_mobile_position( $position ): bool {
		if ( ! wp_is_mobile() ) {
			return false;
		}

		if ( $this->get_media_type() !== Post_Template_Editorial_Meta::LOTTIE ) {
			return false;
		}

		if ( ! $this->get_header_lottie_mobile_position() ) {
			return false;
		}

		if ( $this->get_header_lottie_mobile_position() === Post_Template_Editorial_Meta::HEADER_LOTTIE_BACKGROUND_POSITION_CENTER ) {
			return $position === Post_Template_Editorial_Meta::HEADER_LOTTIE_BACKGROUND_POSITION_CENTER;
		}

		if ( $this->get_header_lottie_mobile_position() === Post_Template_Editorial_Meta::HEADER_LOTTIE_BACKGROUND_POSITION_BELOW_CTA ) {
			return $position === Post_Template_Editorial_Meta::HEADER_LOTTIE_BACKGROUND_POSITION_BELOW_CTA;
		}

		if ( $this->get_header_lottie_mobile_position() === Post_Template_Editorial_Meta::HEADER_LOTTIE_BACKGROUND_POSITION_BELOW_META ) {
			return $position === Post_Template_Editorial_Meta::HEADER_LOTTIE_BACKGROUND_POSITION_BELOW_META;
		}

		return false;
	}


	/**
	 * @return Deferred_Component|null
	 */
	public function get_header_lottie_background_animation(): ?Deferred_Component {
		if ( $this->get_media_type() !== Post_Template_Editorial_Meta::LOTTIE ) {
			return null;
		}

		if ( ! empty( $this->get_lottie_player_args() ) ) {
			$lottie = defer_template_part(
				'components/lottie_player/lottie_player',
				null,
				$this->get_lottie_player_args()
			);

			return defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ 'item-single__subheader-background' ],
				Container_Controller::CONTENT => $lottie,
			] );
		}

		return null;
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function get_header_lottie_media_animation(): ?Deferred_Component {
		if ( $this->get_media_type() !== Post_Template_Editorial_Meta::LOTTIE ) {
			return null;
		}

		if ( ! empty( $this->get_lottie_player_args() ) ) {
			$lottie = defer_template_part(
				'components/lottie_player/lottie_player',
				null,
				$this->get_lottie_player_args()
			);

			return defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ 'item-single__subheader-media' ],
				Container_Controller::CONTENT => $lottie,
			] );
		}

		return null;
	}

	/**
	 * @return string|null
	 */
	public function get_header_background_image(): ?string {
		$image = $this->get_field( Post_Template_Editorial_Meta::HEADER_BACKGROUND_IMAGE );

		return $image ? wp_get_attachment_image_src( $image, Image_Sizes::SIXTEEN_NINE_LARGE )[0] : null;
	}

	public function get_logo(): ?string {
		$logo_src = '';

		if ( $this->get_header_has_dark_background() ) {
			$logo_src = wp_get_attachment_image(
				get_theme_mod( 'site_branding_nav_logo_on_dark_bg' ),
				'medium',
				false,
				[ 'loading' => false ]
			);
		} else {
			$logo_src = wp_get_attachment_image(
				get_theme_mod( 'site_branding_nav_logo' ),
				'medium',
				false,
				[ 'loading' => false ]
			);
		}

		if ( ! $logo_src ) {
			return null;
		}

		return sprintf(
			'<div class="site-header-editorial__logo"><a href="%s" rel="home">%s<span class="screen-reader-text">%s</span></a></div>',
			esc_url( home_url() ),
			$logo_src,
			esc_html( get_bloginfo( 'name' ) ),
		);
	}

	public function get_multiauthor(): ?string {
		$multiauthor = get_field( Post_Meta::MULTIAUTHOR );

		if ( ! $multiauthor ) {
			return '';
		}

		$content = '';

		foreach ( $multiauthor as $authorID ) {
			$user = get_user_by( 'ID', $authorID );

			$content .= get_template_part( 'components/author/author', null, $this->get_author_args( [
				'id'               => $authorID,
				'size'             => 120,
				'show_description' => true,
			] ) );
		}

		return $content;
	}

	public function get_reviewer_as_coauthor(): ?string {
		$add_reviewer_as_coauthor = get_field( Post_Meta::REVIEWER_AS_COAUTHOR );
		$reviewers                = $this->get_reviewers();

		if ( ! $add_reviewer_as_coauthor || ! $reviewers ) {
			return '';
		}

		$content = '';

		foreach ( $reviewers as $reviewer ) {
			$content .= get_template_part( 'components/author/author', null, $this->get_author_args( [
				'id'               => $reviewer['ID'],
				'size'             => 120,
				'show_description' => true,
			] ) );
		}

		return $content;
	}

	public function get_multiple_authors(): array {
		$add_reviewer_as_coauthor = get_field( Post_Meta::REVIEWER_AS_COAUTHOR );
		$reviewers                = $this->get_reviewers();
		$multiauthor              = get_field( Post_Meta::MULTIAUTHOR );

		$authors = [];
		if ( $add_reviewer_as_coauthor && $reviewers ) {
			foreach ( $reviewers as $reviewer ) {
				$authors[] = $reviewer['ID'];
			}
		}

		if ( ! empty( $multiauthor ) ) {
			foreach ( $multiauthor as $authorID ) {
				$authors[] = $authorID;
			}
		}

		return $authors;
	}

	public function get_author_args( array $params = [] ): array {
		$id = ! empty( $params['id'] ) ? $params['id'] : get_post_field( 'post_author', get_the_ID() );

		return [
			Author_Controller::AUTHOR_NAME      => Author_Controller::get_author_display_name( $id ),
			Author_Controller::AUTHOR_ID        => $id,
			Author_Controller::AVATAR_SIZE      => $params['size'] ?: 50,
			Author_Controller::SHOW_DESCRIPTION => $params['show_description'] ?: false,
			Author_Controller::AUTHOR_ABOUT     => $this->get_author_description( $id ),
			Author_Controller::SHOW_LINK_NAME   => array_key_exists( 'show_link_name', $params ) ? $params['show_link_name'] : false,
			Author_Controller::SHOW_LINKS_LIST  => $this->get_author_links( (int) $id ),
		];
	}

	public function get_author_description( $id ): string {
		$descriptions = get_field( User_Meta::DESCRIPTION, 'user_' . (int) $id );

		if ( ! $descriptions ) {
			return $this->get_default_author_description( $id );
		}

		$description = array_filter(
			$descriptions,
			function ( $description ) {
				if ( function_exists( 'pll_current_language' ) ) {
					return
						(int) $description[ User_Meta::DESCRIPTION_SITE ] === get_current_blog_id() &&
						$description[ User_Meta::DESCRIPTION_LANGUAGE ] === pll_current_language();
				}

				return
					(int) $description[ User_Meta::DESCRIPTION_SITE ] === get_current_blog_id() &&
					$description[ User_Meta::DESCRIPTION_LANGUAGE ] === User_Meta::DEFAULT_LANGUAGE;
			}
		);

		if ( ! $description ) {
			return $this->get_default_author_description( $id );
		}

		return '<p>' . wp_kses(
			current( $description )[ User_Meta::DESCRIPTION_CONTENT ],
			[
				'a'      => [
					'href'  => [],
					'title' => [],
				],
				'br'     => [],
				'em'     => [],
				'strong' => [],
			]
		) . '</p>';
	}

	private function get_default_author_description( $id ): string {
		return Author_Controller::get_the_author_meta( 'user_description', (int) $id );
	}

	public function get_reviewers(): ?array {
		$reviewer_contributor = get_field( Post_Meta::REVIEWER_CONTRIBUTOR );

		if ( ! $reviewer_contributor ) {
			return [];
		}

		foreach ( $reviewer_contributor as $entity ) {
			if ( $entity['entity_type'] === 'reviewer' ) {
				return $entity['users'];
			}
		}

		return [];
	}

	/**
	 * @return bool
	 */
	public function hide_bottom_recommendations(): bool {
		return (bool) get_field( Post_Meta::HIDE_BOTTOM_RECOMMENDATIONS );
	}
}
