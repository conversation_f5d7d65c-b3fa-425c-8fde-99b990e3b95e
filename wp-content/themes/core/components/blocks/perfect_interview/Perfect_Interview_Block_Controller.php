<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\perfect_interview;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Blocks\Types\Disclaimer\Disclaimer as Dislclaimer_Block;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\ad_block\Ad_Block_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\author\Author_Controller;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Theme\Config\Image_Sizes;

class Perfect_Interview_Block_Controller extends Abstract_Controller {
	public const CONTAINER_CLASSES 		 = 'container_classes';
	public const CLASSES           		 = 'classes';
	public const ATTRS             		 = 'attrs';
	public const INTERVIEWER 			 = 'interviewer';
	public const SPEAKER_INFORMATION     = 'speaker-information';
	public const SPEAKER 			 	 = 'speaker';
	public const SPEAKER_AVATAR        	 = 'speaker-avatar';
	public const SPEAKER_NAME        	 = 'speaker-name';
	public const SPEAKER_TITLE        	 = 'speaker-title';
	public const SPEAKER_COMPANY         = 'speaker-company';
	public const QA_REPEATER       		 = 'qa-repeater';
	public const FIELD_TYPE       		 = 'field-type';
	public const QA_QUESTION       		 = 'qa-question';
	public const QA_ANSWER       		 = 'qa-answer';
	public const QUOTE_CONTENT       	 = 'quote-content';
	public const QUOTE_AUTHOR       	 = 'quote-author';

	private array $container_classes;
	private array $classes;
	private array $attrs;
	private array $interviewer;
	private array $speaker_information;
	private array $speaker;
	private int $speaker_avatar;
	private string $speaker_name;
	private string $speaker_title;
	private string $speaker_company;
	private array $qa_repeater;
	private string $field_type;
	private string $qa_question;
	private string $qa_answer;
	private string $quote_content;
	private string $quote_author;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		$args = $this->parse_args( $args );

		$this->container_classes       = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes                 = (array) $args[ self::CLASSES ];
		$this->attrs                   = (array) $args[ self::ATTRS ];
		$this->interviewer 			   = (array) $args[ self::INTERVIEWER ];
		$this->speaker_information     = (array) $args[ self::SPEAKER_INFORMATION ];
		$this->speaker 			   	   = (array) $args[ self::SPEAKER ];
		$this->speaker_avatar          = $args[ self::SPEAKER_AVATAR ];
		$this->speaker_name            = (string) $args[ self::SPEAKER_NAME ];
		$this->speaker_title           = (string) $args[ self::SPEAKER_TITLE ];
		$this->speaker_company         = (string) $args[ self::SPEAKER_COMPANY ];
		$this->qa_repeater             = (array) $args[ self::QA_REPEATER ];
		$this->field_type              = (string) $args[ self::FIELD_TYPE ];
		$this->qa_question             = (string) $args[ self::QA_QUESTION ];
		$this->qa_answer               = (string) $args[ self::QA_ANSWER ];
		$this->quote_content           = (string) $args[ self::QUOTE_CONTENT ];
		$this->quote_author            = (string) $args[ self::QUOTE_AUTHOR ];
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::CONTAINER_CLASSES       => [],
			self::CLASSES                 => [],
			self::ATTRS                   => [],
			self::INTERVIEWER 			  => [],
			self::SPEAKER_INFORMATION     => [],
			self::SPEAKER 			 	  => [],
			self::SPEAKER_AVATAR          => null,
			self::SPEAKER_NAME            => '',
			self::SPEAKER_TITLE           => '',
			self::SPEAKER_COMPANY         => '',
			self::QA_REPEATER             => [],
			self::FIELD_TYPE              => '',
			self::QA_QUESTION             => '',
			self::QA_ANSWER               => '',
			self::QUOTE_CONTENT           => '',
			self::QUOTE_AUTHOR            => '',
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [ 'b-perfect-interview--content-repeater__item' ],
			self::CLASSES           => [ 'c-block', 'b-perfect-interview' ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @param Author_Controller|null $author
	 *
	 * @return array
	 */
	public function get_interviewer_image_args( Author_Controller|null $author = null ): array {
		$interviewer = $this->interviewer;

		if ( ! $author && ! $interviewer) {
			return [];
		}

		$avatar_url = '';

		if ( $author !== null ) {
			$avatar_url = $author->get_author_avatar();
		} else {
			$avatar_url = Author_Controller::get_author_avatar_from_id( $interviewer[ 'ID' ] );
		}

		if ( empty( $avatar_url ) ) {
			return [];
		}

		return [
			Image_Controller::IMG_URL      => ! empty( $avatar_url ) ? esc_url( $avatar_url ) : null,
			Image_Controller::IMG_ALT_TEXT => $this->get_interviewer_name( $author ),
			Image_Controller::AS_BG        => false,
			Image_Controller::AUTO_SHIM    => false,
			Image_Controller::USE_LAZYLOAD => true,
			Image_Controller::WRAPPER_TAG  => 'div',
			Image_Controller::CLASSES      => [ 'b-perfect-interview--image' ],
			Image_Controller::IMG_CLASSES  => [ 'b-perfect-interview--image-img' ],
			Image_Controller::SRC_SIZE     => Image_Sizes::SQUARE_XSMALL,
		];
	}

	/**
	 * @return array
	 */
	public function get_speaker(): array {
		$speaker_information = $this->speaker_information;

		if ( ! $speaker_information[ 'speaker' ] ) {
			return [];
		}

		return $speaker_information[ 'speaker' ];
	}

	/**
	 * @return array
	 */
	public function get_speaker_image_args( $speaker_as_user = [] ): array {
		$speaker = $this->speaker_information;
		$avatar_id  = 0;
		$avatar_url = '';

		if ( ! empty( $speaker_as_user ) ) {
			$speaker_id = $speaker_as_user[ 'ID' ];
			$avatar_url = Author_Controller::get_author_avatar_from_id( $speaker_id );
		} else {
			$avatar_id = $speaker[ 'speaker-avatar' ];
		}

		if ( empty( $avatar_id ) && empty( $avatar_url ) ) {
			return [];
		}

		return [
			Image_Controller::IMG_URL      => ! empty( $avatar_url ) ? esc_url( $avatar_url ) : null,
			Image_Controller::IMG_ID       => $avatar_id,
			Image_Controller::IMG_ALT_TEXT => esc_attr( $this->get_speaker_name( $speaker_as_user ) ),
			Image_Controller::AS_BG        => false,
			Image_Controller::AUTO_SHIM    => false,
			Image_Controller::USE_LAZYLOAD => true,
			Image_Controller::WRAPPER_TAG  => 'div',
			Image_Controller::CLASSES      => [ 'b-perfect-interview--image' ],
			Image_Controller::IMG_CLASSES  => [ 'b-perfect-interview--image-img' ],
			Image_Controller::SRC_SIZE     => Image_Sizes::SQUARE_XSMALL,
		];
	}

	/**
	 * @return array
	 */
	public function get_content_repeater(): array {
		if ( ! $this->qa_repeater ) {
			return [];
		}

		return $this->qa_repeater;
	}

	/**
	 * @return string
	 */
	public function get_interviewer_name( Author_Controller|null $author = null ): string {
		if ( $author !== null ) {
			$name = $author->get_author_display_name( $author->get_author_id() );
		} else {
			$interviewer = $this->interviewer;
			$name = $interviewer[ 'display_name' ];
		}

		return $name ?? '';
	}

	/**
	 * @return string
	 */
	public function get_speaker_name( $speaker_as_user = [] ): string {
		$speaker = $this->speaker_information;
		$name = '';

		if ( ! empty( $speaker_as_user ) ) {
			$name = $speaker_as_user[ 'display_name' ];
		} else {
			$name = $speaker[ 'speaker-name' ];
		}

		return $name;
	}

	/**
	 * @return string
	 */
	public function get_speaker_title(): string {
		$speaker = $this->speaker_information;
		$title = $speaker[ 'speaker-title' ];

		return $title ?? '';
	}
}
