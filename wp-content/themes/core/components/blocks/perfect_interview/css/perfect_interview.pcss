/* -----------------------------------------------------------------------------
 *
 * Perfect Interview
 *
 * ----------------------------------------------------------------------------- */

.b-perfect-interview {
	&--content-repeater {
		&__item {
			margin: 0 auto;
			max-width: var(--grid-width-staggered-double);
			width: calc(100% - var(--grid-gutter-small) * 2);

			@media (--viewport-medium) {
				width: calc(100% - var(--grid-gutter) * 2);
			}

			.item-single--perfect-post-layout & {
				width: 100%;

				@media (--viewport-medium) {
					width: calc(100% - var(--grid-gutter) * 2);
				}

				@media (--viewport-large) {
					width: 100%;
				}
			}
		}
	}

	&--question-container {
		width: 100%;

		.b-perfect-interview {
			&--interviewer-block,
			&--speaker-block {
				display: inline-grid;
				grid-template-columns: var(--spacer-60) auto;
				gap: var(--spacer-30);
				width: 100%;
				margin-bottom: var(--spacer-60);
			}

			&__avatar {
				img {
					border-radius: var(--border-radius-circle);
					width: var(--spacer-60);
					height: var(--spacer-60);
					object-fit: cover;
				}
			}

			&--question {
				p {
					font-weight: var(--font-weight-semibold);
				}
			}

			&--answer {
			}
		}
	}

	.l-container {
		&:first-of-type {
			.b-perfect-interview--question-container {
				.b-perfect-interview--interviewer-block {
					margin-top: 0;
				}
			}
		}
	}
}

#main-content {
	.item-single__content {
		.b-perfect-interview--content-repeater {
			.c-block {
				&.b-blockquote-with-avatar {
					margin-top: var(--spacer-20);
					margin-bottom: var(--spacer-70);
				}
			}
		}
	}
}
