/* -----------------------------------------------------------------------------
 *
 * Content Loop Header
 *
 * ----------------------------------------------------------------------------- */

.b-content-loop__header {
	margin-bottom: var(--spacer-40);
	position: relative;

	.c-block__title,
	.c-block__description {
		max-width: var(--grid-width-staggered-double);

		@media (--viewport-medium) {
			max-width: var(--grid-6-col);
		}

		@media (--viewport-large) {
			max-width: var(--grid-7-col);
		}

		@media (--viewport-xlarge) {
			max-width: var(--grid-9-col);

			.acf-block-preview & {
				max-width: 75%;
			}
		}

		.b-content-loop--layout_featured & {
			@media (--viewport-full) {
				padding-right: var(--spacer-60);
			}

			@media (--viewport-large) {
				padding-right: 0;
			}
		}
	}

	.c-content-block__cta-wrap {
		margin-top: var(--spacer-30);

		@media (--viewport-full) {
			position: absolute;
			bottom: 0;
			right: 0;
			max-width: var(--grid-3-col);
		}

		@media (--viewport-large) {
			max-width: calc(var(--grid-4-col) - var(--spacer-60));

			.acf-block-preview & {
				max-width: 25%;
			}
		}
	}

	&.c-content-block {
		max-width: none;
	}
}

/* -----------------------------------------------------------------------------
 *
 * Component: Content Loop
 *
 * ----------------------------------------------------------------------------- */

.b-content-loop {

	&:has(.b-content-loop__filter-wrapper) {
		position: relative;
		z-index: 0;
	}

	&__loop_end {
		text-align: center;

		h5 {
			margin-top: var(--spacer-40);
			color: var(--color-neutral-50);
		}
	}

	&__fallback-message {
		font-size: var(--font-size-body);
		line-height: 1.6;
		font-weight: var(--font-weight-regular);
		font-style: italic;
	}

	.b-cards__slide.swiper-slide .c-card .c-card__cta {
		margin-top: var(--spacer-40);
	}

	.c-card {
		margin-bottom: var(--spacer-40);
		background-color: transparent;
		border-radius: 0;

		&:not(.members-only):not(.c-card--query-type-manual):hover {

			.c-card__title {
				color: var(--color-text);
				text-decoration: none;

				&:hover,
				&:focus,
				&:focus-visible {
					color: var(--color-primary);
					text-decoration: underline;

					.wp-block-group.has-dark-background-color & {
						color: var(--color-white);
					}
				}
			}
		}

		&.c-card--style-inline {
			border-bottom: none;
		}
	}

	.c-card__media {

		.c-image {
			overflow: hidden;
			border-radius: var(--border-radius-media);
		}

		.c-image__image {
			transition: var(--transition);
			border-radius: 0;
			object-fit: cover;

			&:hover,
			&:focus {
				transform: scale(1.1);
			}
		}
	}

	.c-card__content {
		display: flex;
		flex-direction: column;
	}

	.c-card__title {

		.wp-block-group & {
			margin-top: 0;
		}
	}

	.c-card__title-content {
		@mixin t-display-x-small;
		font-size: calc(var(--font-size-heading-xxsmall) + 2px);

		.wp-block-group & {
			@media (--viewport-medium) {
				@mixin t-display-x-small;
				font-size: calc(var(--font-size-heading-xxsmall) + 2px);
			}

			@media (--viewport-small-max) {
				@mixin t-display-x-small;
				font-size: calc(var(--font-size-heading-xxsmall) + 2px);
			}
		}

		.c-card--style-inline & {
			@media (--viewport-medium) {
				@mixin t-display-x-small;
			}

			@media (--viewport-full) {
				@mixin t-display-x-small;
			}

			@media (--viewport-full-max) {
				--font-size-heading-xlarge: var(--font-size-heading-xsmall);
				--font-size-heading-large: var(--font-size-heading-xsmall);
				--font-size-heading: var(--font-size-heading-xsmall);
				--font-size-heading-small: var(--font-size-heading-xsmall);
				--font-size-heading-xsmall: var(--font-size-heading-xsmall);
				--font-size-heading-xxsmall: var(--font-size-heading-xsmall);
				--line-height-heading-xlarge: var(--line-height-heading-xsmall);
				--line-height-heading-large: var(--line-height-heading-xsmall);
				--line-height-heading: var(--line-height-heading-xsmall);
				--line-height-heading-small: var(--line-height-heading-xsmall);
				--line-height-heading-xsmall: var(--line-height-heading-xsmall);
				--line-height-heading-xxsmall: var(--line-height-heading-xsmall);
			}
		}
	}

	.c-card--style-inline {
		.c-card__title-content {
			@media (--viewport-medium) {
				@mixin t-display-x-small;
			}

			@media (--viewport-full) {
				@mixin t-display-x-small;
			}

			@media (--viewport-full-max) {
				--font-size-heading-xlarge: var(--font-size-heading-xsmall);
				--font-size-heading-large: var(--font-size-heading-xsmall);
				--font-size-heading: var(--font-size-heading-xsmall);
				--font-size-heading-small: var(--font-size-heading-xsmall);
				--font-size-heading-xsmall: var(--font-size-heading-xsmall);
				--font-size-heading-xxsmall: var(--font-size-heading-xsmall);
				--line-height-heading-xlarge: var(--line-height-heading-xsmall);
				--line-height-heading-large: var(--line-height-heading-xsmall);
				--line-height-heading: var(--line-height-heading-xsmall);
				--line-height-heading-small: var(--line-height-heading-xsmall);
				--line-height-heading-xsmall: var(--line-height-heading-xsmall);
				--line-height-heading-xxsmall: var(--line-height-heading-xsmall);
			}
		}

		.c-card__description {
			margin-top: var(--spacer-20);
		}
	}

	.c-card__category-link a {

		.wp-block-group & {
			color: var(--color-primary);
		}
	}

	.c-card__date-time-container {
		display: flex;
		flex-wrap: wrap;

		.c-card__date,
		.c-card__time {
			flex: 0 1 auto;

			.icon {
				margin-top: -3px;
			}
		}

		.c-card__date {
			margin-right: var(--spacer-20);
		}
	}

	.c-card__description {
		margin-top: var(--spacer-20);

		p {
			font-size: var(--font-size-body-small);
		}
	}

	.c-card__upcoming-cta {
		display: none;
	}

	.c-card__cta-link {
		.wp-block-group.has-dark-background-color & {
			&:hover,
			&:focus {
				color: var(--color-white) !important;
			}

			&:after {
				background-image: unset;
			}
		}
	}

	.c-card__registration-link {
		margin-top: var(--spacer-10);
		margin-bottom: var(--spacer-10);
	}

	.c-card__cta-link {
		.wp-block-group.has-dark-background-color & {
			&:hover,
			&:focus {
				color: var(--color-white);
			}
		}
	}

	.item-single__author-container {
		font-weight: var(--font-weight-semibold);
		justify-content: flex-start;
		margin-bottom: 0;
		margin-top: var(--spacer-10);

		.acf-block-preview & {
			pointer-events: none;
		}
	}

	.item-single__author-name {
		font-size: var(--font-size-body-small);
		color: var(--color-text);

		.t-sink & a {
			&, &:focus, &:hover{
				color: var(--color-text);
				text-decoration-color: var(--color-text);
			}

			.wp-block-group.has-dark-background-color & {
				text-decoration: none;
			}
		}

		.t-sink .has-dark-background-color & a {
			&, &:focus, &:hover{
				color: var(--color-white);
				text-decoration-color: var(--color-white);
			}
		}
	}

	/* CASE: Columns */

	&.b-content-loop--layout_columns {

		.c-card:nth-child(1),
		.c-card:nth-child(2) {
			border-top: 1px solid var(--color-grey-light);
		}

		.c-block__cta-link {
			display: table !important;
			margin: var(--spacer-60) auto 0 auto;
		}

		.c-content-block__cta-link {
			display: none;
		}

		.item-single__author-container {
			margin-top: var(--spacer-20);
		}

		.item-single__author-image {
			display: none;
		}
	}

	/* CASE: Feature */

	&.b-content-loop--layout_featured {

		.g-2-up {
			@media (--viewport-medium) {
				margin: 0 var(--grid-gutter-small-half-negative);
			}

			@media (--viewport-full) {
				margin: 0 var(--grid-gutter-half-negative);
			}

			& > * {
				@media (--viewport-medium) {
					width: 100%;
					margin: 0 var(--grid-gutter-small-half);
				}

				@media (--viewport-full) {
					width: calc(50% - var(--grid-gutter));
					margin: 0 var(--grid-gutter-half);
				}
			}
		}

		.b-content-loop__featured {
			margin-bottom: 0;

			.c-card {
				border-bottom: 1px solid var(--color-neutral-30);
				padding-bottom: var(--spacer-30);
				margin-bottom: 0;

				@media (--viewport-medium) {
					border-bottom: none;
					flex-flow: row wrap;
				}
			}

			.c-card__media {
				@media (--viewport-medium) {
					flex: 1 1 300px;
					margin-right: var(--spacer-30);
				}
			}

			.c-card__title-content {
				@media (--viewport-medium) {
					@mixin t-display-xxx-small;
				}

				@media (--viewport-medium-max) {
					--font-size-heading-xlarge: var(--font-size-heading-small);
					--font-size-heading-large: var(--font-size-heading-small);
					--font-size-heading: var(--font-size-heading-small);
					--font-size-heading-small: var(--font-size-heading-small);
					--font-size-heading-xsmall: var(--font-size-heading-small);
					--font-size-heading-xxsmall: var(--font-size-heading-small);
					--line-height-heading-xlarge: var(--line-height-heading-small);
					--line-height-heading-large: var(--line-height-heading-small);
					--line-height-heading: var(--line-height-heading-small);
					--line-height-heading-small: var(--line-height-heading-small);
					--line-height-heading-xsmall: var(--line-height-heading-small);
					--line-height-heading-xxsmall: var(--line-height-heading-small);
				}

				@media (--viewport-full) {
					@mixin t-display;
				}

				@media (--viewport-full-max) {
					--font-size-heading-xlarge: var(--font-size-heading);
					--font-size-heading-large: var(--font-size-heading);
					--font-size-heading: var(--font-size-heading);
					--font-size-heading-small: var(--font-size-heading);
					--font-size-heading-xsmall: var(--font-size-heading);
					--font-size-heading-xxsmall: var(--font-size-heading);
				}
			}

			.c-card__content {
				@media (--viewport-medium) {
					flex: 1 1 320px;
					margin-right: var(--spacer-30);
				}
			}

			.item-single__author-image {
				display: none;

				@media (--viewport-full) {
					display: block;
				}
			}
		}

		.b-content-loop__secondary {
			.c-card__title-content {
				@mixin t-display-xxx-small;

				@media (--viewport-full) {
					@mixin t-display-small;
				}
			}

			.c-card__cta {
				margin-top: 0;
			}
		}
	}

	&.b-content-loop--layout_featured_ad {
		.c-ad-block-featured {
			.l-container {
				padding: var(--spacer-20) 0;
			}

			.c-ad-block__container {
				.c-ad-block__description_title {
					@media (--viewport-medium) {
						color: var(--color-text);

						.wp-block-group.has-dark-background-color & {
							color: var(--color-white);
						}
					}
				}

				.c-ad-block__cta-link {
					@media (--viewport-medium) {
						color: var(--color-primary);
					}

					&:hover,
					&:focus {
						@media (--viewport-medium) {
							color: var(--color-cta-primary-hover);
						}

						&:before,
						&:after {
							@media (--viewport-medium) {
								color: var(--color-cta-primary-hover);
							}
						}
					}

					&:before,
					&:after {
						@media (--viewport-medium) {
							color: var(--color-primary);
						}
					}
				}
			}
		}
	}

	/* CASE: Single Left, Right or with Sidebar */

	&.b-content-loop--layout_single_left,
	&.b-content-loop--layout_single_right,
	&.b-content-loop--layout_single_sidebar {

		.c-card {
			margin-bottom: var(--spacer-40);

			@media (--viewport-medium) {
				max-width: 90%;
			}

			@media (--viewport-full) {
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;
				max-width: 100%;
			}
		}

		.c-card__media {
			margin-bottom: var(--spacer-40);
			width: 100%;
			min-width: 100%;

			@media (--viewport-full) {
				width: calc(50% - (var(--grid-gutter) / 2));
				min-width: calc(50% - (var(--grid-gutter) / 2));
				margin-right: var(--grid-gutter);
				margin-bottom: 0;
			}
		}

		.c-card__title-content {
			.b-content-loop:not(.b-content-loop--layout_single_sidebar) {
				@media (--viewport-medium) {
					@mixin t-display-small;
				}

				@media (--viewport-medium-max) {
					--font-size-heading-xlarge: var(--font-size-heading-small);
					--font-size-heading-large: var(--font-size-heading-small);
					--font-size-heading: var(--font-size-heading-small);
					--font-size-heading-small: var(--font-size-heading-small);
					--font-size-heading-xsmall: var(--font-size-heading-small);
					--font-size-heading-xxsmall: var(--font-size-heading-small);
					--line-height-heading-xlarge: var(--line-height-heading-small);
					--line-height-heading-large: var(--line-height-heading-small);
					--line-height-heading: var(--line-height-heading-small);
					--line-height-heading-small: var(--line-height-heading-small);
					--line-height-heading-xsmall: var(--line-height-heading-small);
					--line-height-heading-xxsmall: var(--line-height-heading-small);
				}

				@media (--viewport-full) {
					@mixin t-display-large;
				}

				@media (--viewport-full-max) {
					--font-size-heading-xlarge: var(--font-size-heading-large);
					--font-size-heading-large: var(--font-size-heading-large);
					--font-size-heading: var(--font-size-heading-large);
					--font-size-heading-small: var(--font-size-heading-large);
					--font-size-heading-xsmall: var(--font-size-heading-large);
					--font-size-heading-xxsmall: var(--font-size-heading-large);
				}
			}
		}

		.c-card__content {
			margin: 0;

			@media (--viewport-full) {
				width: 50%;
			}
		}

		&.b-content-loop--without-thumbnail {
			.c-card__content {
				width: 100%;
			}
		}

		.c-card__description {

			p {
				font-size: var(--font-size-body-small);

				@media (--viewport-medium) {
					font-size: var(--font-size-body);
				}
			}
		}

		.c-card__cta {
			display: block;
		}

		.c-card__members-only-warning {
			margin-top: var(--spacer-40);
		}
	}

	/* CASE: Single Right */

	&.b-content-loop--layout_single_right {

		.c-card {
			flex-flow: column nowrap;

			@media (--viewport-medium) {
				flex-direction: column;
			}

			@media (--viewport-full) {
				flex-direction: row-reverse;
			}

			.c-card__media {
				margin-right: 0;

				@media (--viewport-full) {
					margin-left: var(--grid-gutter);
				}
			}
		}
	}

	&.b-content-loop--layout_single_left,
	&.b-content-loop--layout_single_right {
		.c-card__title-content {
			@mixin t-display-xxx-small;

			@media (--viewport-full) {
				@mixin t-display-small;
			}
		}
	}

	/* CASE: Multiple */

	&.b-content-loop--layout_multiple {
		overflow: hidden;

		@media (--viewport-full) {
			padding-top: var(--spacer-20);
		}

		.b-cards__slider {
			@media (--viewport-medium-max) {
				margin: 0;
			}

			@media (--viewport-medium) and (--viewport-medium-max) {
				width: calc(100% - var(--grid-gutter-medium));
			}

			@media (--viewport-full) {
				padding: 0;
				display: flex;
				flex-direction: column-reverse;
			}

			.swiper-button-prev {
				@media (--viewport-full) and (max-width: 1319px) {
					left: calc( var(--spacer-40) * -1 );
				}
			}

			.swiper-button-next {
				@media (--viewport-full) {
					right: calc( var(--spacer-20) - 1px );
				}

				@media (min-width: 1320px) {
					right: calc( var(--spacer-30) + 5px );
				}
			}

			.c-slider__main {
				overflow-x: hidden;
				width: calc(100% - var(--grid-gutter) + 15px);

				@media (--viewport-full) {
					width: calc(100% - var(--grid-gutter));
					margin: 0;
				}
			}

			.c-slider__wrapper {
				@media (--viewport-full) {
					width: calc(100% - var(--grid-gutter));
				}
			}

			.c-slider__arrows {
				@media (min-width: 1320px) {
					position: absolute;
					top: 50%;
					left: calc(-1 * 20px);
					width: calc(100% + 20px + 20px);
					justify-content: space-between;
					margin-top: 0;
					transform: translateY(-50%);
					z-index: 10;
					pointer-events: none;

					.swiper-button-prev, .swiper-button-next {
						width: var(--spacer-40);
						margin: 0;
						pointer-events: all;

						&:after {
							font-size: var(--font-size-heading);
						}
					}
				}

				@media (min-width: 1400px) {
					left: calc(-1 * var(--spacer-50));
					width: calc(100% + var(--spacer-50) + var(--spacer-50));
				}
			}
		}

		.b-cards__slide {

			@media (--viewport-full) {
				&:first-of-type {
					margin-left: 0;
				}
			}

			.c-card {
				position: relative;
				max-width: 100%;
				-webkit-user-select: none;
				-ms-user-select: none;
				user-select: none;
			}

			.c-card__category-link,
			.item-single__author-container {
				position: relative;
				z-index: 2;
			}

			.item-single__author-container {
				margin-top: var(--spacer-20);
			}

			.item-single__author-image {
				display: none;
			}
		}

		.c-card__title-content {
			@mixin t-display-xxx-small;

			@media (--viewport-full) {
				@mixin t-display-small;
			}

			@media (--viewport-full-max) {
				--font-size-heading-xlarge: var(--font-size-heading-small);
				--font-size-heading-large: var(--font-size-heading-small);
				--font-size-heading: var(--font-size-heading-small);
				--font-size-heading-small: var(--font-size-heading-small);
				--font-size-heading-xsmall: var(--font-size-heading-small);
				--font-size-heading-xxsmall: var(--font-size-heading-small);
				--line-height-heading-xlarge: var(--line-height-heading-small);
				--line-height-heading-large: var(--line-height-heading-small);
				--line-height-heading: var(--line-height-heading-small);
				--line-height-heading-small: var(--line-height-heading-small);
				--line-height-heading-xsmall: var(--line-height-heading-small);
				--line-height-heading-xxsmall: var(--line-height-heading-small);
			}
		}

		.g-2-up {
			.c-card__title-content {
				@mixin t-display-xxx-small;

				@media (--viewport-full) {
					@mixin t-display-small;
				}

				@media (--viewport-full-max) {
					--font-size-heading-xlarge: var(--font-size-heading);
					--font-size-heading-large: var(--font-size-heading);
					--font-size-heading: var(--font-size-heading);
					--font-size-heading-small: var(--font-size-heading);
					--font-size-heading-xsmall: var(--font-size-heading);
					--font-size-heading-xxsmall: var(--font-size-heading);
					--line-height-heading-xlarge: var(--line-height-heading);
					--line-height-heading-large: var(--line-height-heading);
					--line-height-heading: var(--line-height-heading);
					--line-height-heading-small: var(--line-height-heading);
					--line-height-heading-xsmall: var(--line-height-heading);
					--line-height-heading-xxsmall: var(--line-height-heading);
				}
			}

			.c-card__upcoming-cta {
				display: block;
			}
		}
	}

	/* CASE: Multiple with Ad */

	&.b-content-loop--layout_multiple_ad {
		overflow: hidden;

		@media (--viewport-full) {
			padding-top: var(--spacer-20);
		}

		.g-3-up {
			display: grid;
			grid-template-columns: 25% 75%;
			grid-template-areas: "ad content";

			@media (--viewport-medium-max) {
				grid-template-columns: 100%;
				grid-template-areas: "content" "ad";
			}
		}

		.g-2-up {
			display: grid;
			grid-template-columns: 33.33% 33.33% 33.33%;
			grid-template-areas: "ad content content";

			@media (--viewport-medium-max) {
				grid-template-columns: 100%;
				grid-template-areas: "content" "content" "ad";
			}

			.c-card {
				width: calc(100% - var(--grid-gutter));
			}

			.c-card__title-content {
				@mixin t-display-xxx-small;

				@media (--viewport-full) {
					@mixin t-display-small;
				}

				@media (--viewport-full-max) {
					--font-size-heading-xlarge: var(--font-size-heading);
					--font-size-heading-large: var(--font-size-heading);
					--font-size-heading: var(--font-size-heading);
					--font-size-heading-small: var(--font-size-heading);
					--font-size-heading-xsmall: var(--font-size-heading);
					--font-size-heading-xxsmall: var(--font-size-heading);
					--line-height-heading-xlarge: var(--line-height-heading);
					--line-height-heading-large: var(--line-height-heading);
					--line-height-heading: var(--line-height-heading);
					--line-height-heading-small: var(--line-height-heading);
					--line-height-heading-xsmall: var(--line-height-heading);
					--line-height-heading-xxsmall: var(--line-height-heading);
				}
			}

			.c-card__upcoming-cta {
				display: block;
			}
		}

		.b-content-loop-ad {
			grid-area: ad;
			z-index: 1;
			position: relative;
			background-color: var(--color-neutral-0);
			width: calc(100% - var(--grid-gutter));

			@media (--viewport-medium-max) {
				margin: 0 auto;
				width: 100%;
				padding: 0 calc(var(--grid-gutter-small) / 2);
			}

			.wp-block-group.has-dark-background-color & {
				background-color: var(--color-background-dark);
			}

			&__container {
				z-index: 2;
				position: relative;
			}

			&::after {
				content: '';
				display: block;
				width: calc(100vw / 2);
				background-color: var(--color-neutral-0);
				height: 100%;
				position: absolute;
				top: -4px;
				right: -4px;

				.wp-block-group.has-dark-background-color & {
					background-color: var(--color-background-dark);
				}
			}

			@media (--viewport-full) {
				&--right {
					.b-content-loop-ad::after {
						left: -4px;
					}

					&.g-3-up {
						grid-template-columns: 75% 25%;
						grid-template-areas: "content ad";
					}

					&.g-2-up {
						grid-template-areas: "content content ad";
					}
				}
			}
		}

		.b-content-loop-ad--left {
			.b-cards__slider {
				.c-slider__arrows {
					z-index: 1;

					@media (min-width: 1320px) {
						left: calc(-272px - var(--grid-gutter) - 20px);
					}
					@media (min-width: 1400px) {
						left: calc(-272px - var(--grid-gutter) - var(--spacer-50));
					}
				}
			}
		}

		.b-content-loop-ad--right {
			.b-cards__slider {
				.c-slider__arrows {
					z-index: 1;

					@media (min-width: 1320px) {
						right: calc(-272px - var(--grid-gutter) - 20px);
					}
					@media (min-width: 1400px) {
						right: calc(-272px - var(--grid-gutter) - var(--spacer-50));
					}
				}
			}
		}

		.b-cards__slider {
			grid-area: content;
			width: 100%;
			flex: 1;

			@media (--viewport-full) {
				padding: 0 var(--grid-gutter-half);
				display: flex;
				flex-direction: column-reverse;
			}

			.c-slider__main {
				overflow-x: hidden;

				@media (--viewport-full) {
					width: 100%;
				}
			}

			.c-slider__wrapper {
				@media (--viewport-full) {
					width: calc(100% + var(--grid-gutter));
				}
			}

			.c-slider__arrows {
				@media (min-width: 1320px) {
					position: absolute;
					top: calc(272px / 2);
					width: calc(100% + 272px + var(--grid-gutter) + 20px + 20px);
					justify-content: space-between;
					margin-top: 0;
					transform: translateY(-50%);
					z-index: 10;
					pointer-events: none;

					.swiper-button-prev, .swiper-button-next {
						width: var(--spacer-40);
						margin: 0;
						pointer-events: all;

						&:after {
							font-size: var(--font-size-heading);
						}
					}
				}

				@media (min-width: 1400px) {
					width: calc(100% + 272px + var(--grid-gutter) + var(--spacer-50) + var(--spacer-50));
				}
			}
		}

		.b-cards__slide {

			@media (--viewport-full) {
				&:first-of-type {
					margin-left: 0;
				}
			}

			.c-card {
				position: relative;
				max-width: 100%;
				-webkit-user-select: none;
				-ms-user-select: none;
				user-select: none;
			}

			.c-card__category-link,
			.item-single__author-container {
				position: relative;
				z-index: 2;
			}

			.item-single__author-image {
				display: none;
			}

			&:has(.c-card__description) {
				.item-single__author-container {
					margin-top: 0;
				}
			}
		}

		.c-card__title-content {
			@mixin t-display-xxx-small;

			@media (--viewport-full) {
				@mixin t-display-small;
			}

			@media (--viewport-full-max) {
				--font-size-heading-xlarge: var(--font-size-heading-small);
				--font-size-heading-large: var(--font-size-heading-small);
				--font-size-heading: var(--font-size-heading-small);
				--font-size-heading-small: var(--font-size-heading-small);
				--font-size-heading-xsmall: var(--font-size-heading-small);
				--font-size-heading-xxsmall: var(--font-size-heading-small);
				--line-height-heading-xlarge: var(--line-height-heading-small);
				--line-height-heading-large: var(--line-height-heading-small);
				--line-height-heading: var(--line-height-heading-small);
				--line-height-heading-small: var(--line-height-heading-small);
				--line-height-heading-xsmall: var(--line-height-heading-small);
				--line-height-heading-xxsmall: var(--line-height-heading-small);
			}
		}

		.c-card__description {
			@mixin t-body;
		}

		.c-ad-block-inline {
			.l-container {
				padding: 0;
			}

			.c-ad-block {
				.has-dark-background-color & {
					border: 2px solid var(--color-white);
				}

				@media (--viewport-medium-max) {
					margin-top: var(--spacer-60);
				}

				&__container {
					flex-direction: column;
					gap: var(--spacer-30);
				}

				&__logo {
					max-width: 250px;

					@media (--viewport-medium-max) {
						flex: 1;
					}
				}

				&__description {
					padding: 0;
					text-align: center;
					font-size: var(--font-size-body-small);
				}
			}
		}
	}

	/* CASE: List */

	&.b-content-loop--layout_list,
	&.b-content-loop--layout_list_with_description {

		.g-2-up {

			& > * {
				@media (--viewport-medium) {
					width: 100%;
					margin: 0 var(--grid-gutter-small-half);
				}

				@media (--viewport-full) {
					width: calc(50% - var(--grid-gutter));
					margin: 0 var(--grid-gutter-half);
				}
			}
		}

		.c-card {
			@media (--viewport-full) {
				padding: var(--spacer-30) 0;
			}

			&:not(:last-child) {
				border-bottom: 1px solid var(--color-neutral-30);

				@media (--viewport-medium) {
					border-bottom: none;
				}
			}

			.item-single--multiauthor {
				.item-single__avatars_wrapper {
					display: none;
				}
			}
		}

		.c-card__content {
			height: 100%;
		}

		.c-card__cta {
			.c-block__cta-link {
				display: table;
				margin: 0 auto 0 0;
			}
		}

		.c-block__cta-link {
			display: table;
			margin: 0 auto 0 auto;

			.wp-block-group & {
				display: table;
			}

			&:hover {
				color: var(--color-button-primary-text);
			}
		}

		.c-card__title {
			margin-bottom: var(--spacer-20);
		}

		.c-card__title-content {
			@mixin t-display-xxx-small;

			@media (--viewport-full) {
				@mixin t-display-small;
			}
		}

		.c-card__description {
			margin-bottom: 0;
			margin-top: 0;
		}

		.item-single__author-container {
			margin-top: var(--spacer-20);
		}
	}

	/* CASE: List with Ad */

	&.b-content-loop--layout_single_left {
		&.b-content-loop--layout_list_ad {
			margin-top: 0;
			margin-bottom: 0;

			@media (--viewport-large) {
				display: flex;

				.c-card {
					flex: 0 0 70%;
					width: 70%;
				}

				.c-ad-block-inline {
					margin-top: 0;
					flex: 0 0 30%;
					width: 30%;
				}
			}

			.c-ad-block-inline {
				margin-bottom: var(--spacer-40);
				margin-top: 0;

				.l-container {
					display: flex;
					min-height: 100%;
					padding-right: 0;

					@media (--viewport-full-max) {
						padding: 0;
						justify-content: center;
					}
				}

				.c-ad-block {
					display: flex;
					align-items: center;
					padding: var(--spacer-40);

					&__description {
						text-align: center;
						padding: 0;
						padding-top: var(--spacer-20);
						padding-bottom: var(--spacer-20);
					}
				}
			}

			.c-card__content {
				.b-content-loop--without-thumbnail & {
					width: 100%;
				}
			}
		}
	}

	&--text-balanced {
		.b-content-loop__header {
			.c-block__title,
			.c-block__description {
				@supports (text-wrap: balance) {
					text-wrap: balance;
				}
			}
		}
	}

	/* CASE: Single Column */

	& &--list-single-column {
		.c-card__title {
			margin-bottom: var(--spacer-20);
		}

		.c-card__description {
			margin-top: 0;
		}

		.item-single__author-container {
			margin-top: 0;
		}
	}

	/* CASE: With Sidebar */
	&--with-sidebar {
		display: grid;
		grid-auto-flow: column;
		grid-template-columns: var(--grid-sidebar-col) 1fr;
        grid-column-gap: var(--spacer-70);

		@media(--viewport-medium-max) {
			grid-auto-flow: row;
    		grid-template-columns: auto;
		}

		.b-content-loop__sidebar {
			@media(--viewport-small-max) {
				order: 2;
			}

			&__wrapper {
				background-color: var(--color-background-light);
				padding: var(--spacer-40);
				border-radius: var(--border-radius-media);

				.has-dark-background-color & {
					background-color: transparent;
					border: 1px solid var(--color-neutral-30);
				}

				> h3 {
					line-height: 1.5;
					letter-spacing: 1.5px;
					font-weight: var(--font-weight-semibold);
					text-transform: uppercase;
					color: var(--color-neutral-50);
					font-family: var(--font-family-base);
					font-size: var(--font-size-special-small);
				}
			}

			.c-card__media,
			.c-card__description,
			.item-single__author-container,
			.c-card__cta {
				display: none;
			}

			.c-card {
				padding-top: var(--spacer-40);

				&:not(:first-of-type) {
					border-top: 1px solid var(--color-neutral-30);
				}

				&:last-of-type {
					margin-bottom: 0;
				}

				&__title-content {
					font-size: var(--font-size-heading-xxsmall);
				}
			}

			.c-ad-block-inline {
				.l-container {
					padding: 0;
				}

				.c-ad-block__container {
					display: block;
				}

				.c-ad-block__description {
					padding: 0;
				}
			}
		}

		.b-content-loop__content-wrapper {
			@media(--viewport-full) {
				padding-top: var(--spacer-40);
			}
		}

		.item-single__author-container {
			margin-top: 0;
		}

		.item-single__author-image {
			width: 30px;
			height: 30px;
		}
	}

	/* CASE: List Rows */
	&--list-rows {
		.c-card {
			@media (--viewport-full) {
				&:nth-child(-n+3) {
					.c-card__description {
						font-size: var(--font-size-heading-xxsmall);
						line-height: var(--line-height-heading-xxsmall);
					}
				}
			}

			&:nth-child(n+4) {
				padding-top: var(--spacer-40);
    			border-top: 1px solid var(--color-neutral-30);
			}

			@media (--viewport-medium-max) {
				&:nth-child(n+3) {
					padding-top: var(--spacer-40);
    				border-top: 1px solid var(--color-neutral-30);
				}

				&:nth-child(-n+2) {
					.c-card__description {
						font-size: var(--font-size-heading-xxsmall);
						line-height: var(--line-height-heading-xxsmall);
					}
				}
			}

			@media (--viewport-small-max) {
				&:nth-child(n+2) {
					padding-top: var(--spacer-40);
					border-top: 1px solid var(--color-neutral-30);
				}

				&:nth-child(-n+1) {
					.c-card__description {
						font-size: var(--font-size-heading-xxsmall);
						line-height: var(--line-height-heading-xxsmall);
					}
				}
			}

			&:nth-child(-n+3) {
				.c-card__description {
					font-size: var(--font-size-body);
					line-height: var(--line-height-body);
				}
			}

			&__description {
				-webkit-box-orient: vertical;
				display: -webkit-box;
				-webkit-line-clamp: 3;
				overflow: hidden;
			}
		}

		&-hide-image {
			.c-card {
				&:nth-child(n+4) {
					.c-card__media {
						display: none;
					}
				}

				@media (--viewport-medium-max) {
					&:nth-child(n+3) {
						.c-card__media {
							display: none;
						}
					}
				}

				@media (--viewport-small-max) {
					&:nth-child(n+2) {
						.c-card__media {
							display: none;
						}
					}
				}
			}
		}
	}

	&.b-content-loop--layout_single_sidebar {
		.c-card__description {
			font-size: var(--font-size-body);
			line-height: var(--line-height-body);
		}

		.c-card__title-content {
			@mixin t-display-small;
			margin-bottom: 0;
		}
	}

	button {
		margin: 0 auto;
    	display: block;

		&.a-cta--secondary {
			border-top: 0;
			border-left: 0;
			border-right: 0;
			outline: 0;
			background-color: transparent;
		}

		&.c-block__btn-filter {
			margin: 0;
		}
	}

	&__filter-wrapper {
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		row-gap: var(--spacer-20);
		margin-bottom: var(--spacer-20);

		@media (--viewport-small-max) {
			flex-direction: column;
			align-items: flex-start;
    		row-gap: var(--spacer-10);
		}
	}

	&__selector {
		cursor: pointer;
		position: relative;
		display: block;
		min-width: 250px;

		&__placeholder {
			position: relative;
			z-index: 4;
			border: var(--form-border-size) solid var(--form-border-color);
			padding: var(--spacer-10) var(--spacer-30);
			border-radius: var(--form-border-radius);
			transition: all .2s ease-in;
			background-color: var(--color-white);

			&:after {
				content: "\e90d";
				position: absolute;
				right: 0;
				top: 50%;
				transform: translateY(-50%) translateX(-50%);
				line-height: var(--global-line-height-heading);
				font-size: var(--font-size-body);
				font-family: core-icons !important;
				transition: .3s transform;
			}
		}

		&__list {
			display: none;
			position: absolute;
			top: calc(100% - 2px);
			right: 0;
			left: 0;
			z-index: 3;
			margin: 0;
			padding: 0;
			list-style: none;
			background-color: var(--color-white);
			border: var(--form-border-size) solid var(--color-black);
			border-top: none;
			border-bottom-left-radius: var(--form-border-radius);
    		border-bottom-right-radius: var(--form-border-radius);
			max-height: 250px;
			overflow: scroll;
			padding: var(--spacer-10) var(--spacer-20);
		}

		input[type="checkbox"] {
			-webkit-appearance: none;
			appearance: none;
			background-color: #fff;
			margin: 0;
			font: inherit;
			color: #000;
			width: var(--spacer-20);
			height: var(--spacer-20);
			border: 1px solid var(--color-black);
			border-radius: var(--form-border-radius);
			transform: translateY(2px);
			display: grid;
			place-content: center;

			&:before {
				content: "";
				width: var(--spacer-10);
				height: var(--spacer-10);
				transform: scale(0);
				transition: transform .3s ease-in-out;
				box-shadow: inset var(--spacer-10) var(--spacer-10) var(--color-primary);
			}

			&:checked:before {
				transform: scale(1);
			}
		}

		&__label {
			display: grid !important;
			grid-template-columns: var(--spacer-20) auto;
			gap: var(--spacer-10);
			cursor: pointer;
			font-weight: var(--font-weight-regular) !important;
		}

		&--active {
			.b-content-loop__selector__placeholder {
				border-color: var(--color-black);

				&:after {
					transition: .3s transform;
					transform: translateY(-50%) translateX(-50%) rotate(90deg);
				}
			}

			.b-content-loop__selector__list {
				display: block;
			}
		}
	}

	&__selector-label {
		font-weight: var(--font-weight-semibold);
	}

	&--loading {
		opacity: .5;
		pointer-events: none;
		cursor: none;
	}

	&__load-more-container {
		height: var(--spacer-40);
	}
}

.b-content-loop__secondary {

	.b-content-loop.b-content-loop--layout_featured & {
		margin-top: var(--spacer-30);

		@media (--viewport-full) {
			margin-top: 0;
		}

		.item-single__author-container {
			margin-top: 0;
		}

		.item-single__author-image {
			display: none;
		}

		.c-card__description {
			display: none;

			@media (--viewport-medium) {
				display: block;
			}
		}

		.c-card {

			&:not(:last-child) {
				border-bottom: 1px solid var(--color-neutral-30);

				@media (--viewport-medium) {
					border-bottom: none;
				}
			}
		}
	}

	.c-card:first-child {
		padding-top: 0;
	}

	.c-block__cta-link {
		margin-top: var(--spacer-20);
	}
}

.b-content-loop__secondary__cta {
	margin-top: var(--spacer-10);
	text-align: center;

	@media (--viewport-full) {
		text-align: left;
	}
}

.c-block__cta-link {
	overflow: hidden !important;

	&--loading {
		position: relative;
		pointer-events: none;

		&:before {
			content: '';
			position: absolute;
			width: 100%;
			height: 100%;
			left: 0;
			top: 0;
			background-color: rgba(255, 255, 255, .4);
		}

		&:after, &.a-cta--secondary:after {
			content: '';
			display: block;
			width: 16px;
			height: 16px;
			margin: auto;
			position: absolute;
			inset: 0;
			border-radius: 50%;
			border: 3px solid transparent;
			border-top-color: var(--global-color-primary-in-dark-bg-hover);
			animation: link-loading 1s linear infinite;
		}
	}
}

/* -----------------------------------------------------------------------------
 *
 * Content Loop in Perfect Post template
 *
 * ----------------------------------------------------------------------------- */

.item-single--perfect-post-layout {
	.item-single {
		&__content {
			.b-content-loop {
				&--layout_multiple {
					.c-slider__main {
						width: calc( 100vw - var(--grid-margin-medium) * 4 );

						@media (--viewport-small-max) {
							width: calc( 100vw - var(--grid-margin-small) * 2 );
						}

						@media (--viewport-full) {
							width: calc( 100vw - var(--grid-margin-medium) * 4 - var(--spacer-50) * 2 );
						}

						@media (--viewport-large) {
							width: calc(var(--grid-width) - var(--grid-sidebar-col) - var(--spacer-80) - var(--spacer-70) * 2 - var(--spacer-50) * 2 ) !important;
						}
					}

					.c-card {
						margin-bottom: 0;

						&__meta-primary-container {
							display: none;
						}

						&__title-content {
							@media (--viewport-full) {
								@mixin t-display;
							}
						}

						&__media {
							margin-bottom: var(--spacer-30);
						}

						&__description {
							margin-top: var(--spacer-10);
							margin-bottom: 0;
						}

						&__cta {
							margin-top: var(--spacer-30);
						}
					}

					.item-single__author-container {
						display: none;
					}

					.c-slider__button {
						&:after {
							font-size: var(--global-font-size-heading-xlarge) !important;
						}

						&.swiper-button-prev {
							@media (--viewport-large) {
								left: calc(var(--spacer-20) * 2);
							}

							@media (min-width: 1320px) {
								left: calc( var(--spacer-50) + var(--spacer-40) );
							}
						}

						&.swiper-button-next {
							@media (--viewport-large) {
								right: calc(var(--spacer-20) * 2);
							}

							@media (min-width: 1320px) {
								right: calc( var(--spacer-50) + var(--spacer-40) );
							}
						}
					}
				}

				&--layout_list {
					.c-card {
						&:last-of-type {
							padding-bottom: 0;
						}

						&:first-of-type {
							padding-top: 0;
						}
					}
				}
			}

			.wp-block-group {
				@media (--viewport-medium-max) {
					padding: var(--spacer-60) var(--spacer-40);
				}

				& + .wp-block-group {
					margin-top: calc( var(--spacer-50) * 2 );
				}

				.l-container {
					padding-left: 0;
					padding-right: 0;
				}

				.b-content-loop {
					overflow: visible !important;
					margin-top: 0 !important;
					margin-bottom: 0 !important;

					&--layout_multiple {
						.c-slider__main {
							@media (--viewport-small-max) {
								width: calc( 100vw - var(--grid-margin-small) * 2 - var(--spacer-40) * 2 );
							}
						}
					}

					.b-cards__slider {
						padding: 0;
						margin: 0;
					}

					.c-slider__button {
						&.swiper-button-prev {
							@media (--viewport-full) {
								left: var(--spacer-20);
							}

							@media (min-width: 1320px) {
								left: calc(var(--spacer-60) + 5px);
							}
						}

						&.swiper-button-next {
							@media (--viewport-full) {
								right: var(--spacer-20);
							}

							@media (min-width: 1320px) {
								right: calc(var(--spacer-60) + 5px);
							}
						}
					}

					.c-block__cta-link {
						&:not(.c-card__cta-link) {
							display: none;
						}
					}

					&__header {
						.c-content-block__cta {
							display: block;
						}
					}
				}

				&.has-dark-background-color {
					.b-content-loop {
						.c-card__cta {
							.c-card__cta-link {
								color: var(--color-secondary);

								&:before,
								&:after {
									color: var(--color-secondary);
									background-image: none;
								}
							}
						}
					}
				}
			}
		}
	}

	.b-content-loop {
		&__description {
			@media (--viewport-full) {
				padding-right: calc(var(--grid-3-col) - var(--spacer-60));
			}
		}

		&__header {
			.c-content-block__cta-wrap {
				max-width: var(--grid-3-col);
			}

			.c-content-block__cta,
			.c-content-block__cta-link {
				display: block;
			}
		}

		.c-card__cta {
			margin-top: var(--spacer-10);
		}

		.l-container {
			& > .c-block__cta-link {
				display: none;
			}
		}
	}
}

@keyframes link-loading {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
