/* -----------------------------------------------------------------------------
 *
 * Component: Block: Shortlist
 *
 * ----------------------------------------------------------------------------- */

.b-shortlist {
	background-color: var(--color-neutral-10);
	margin-top: 0;
	margin-bottom: 0;
	padding-top: 72px;
	padding-bottom: 72px;

	@media(--viewport-medium) {
		margin-top: 0;
		margin-bottom: 0;
		padding-top: 72px;
		padding-bottom: 72px;
	}

	&:first-child {
		padding-top: 0;
	}

	& + :not(.c-block):not(.wp-block-group) {
		margin-top: 80px;

		@media(--viewport-medium) {
			margin-top: 120px;
		}
	}

	.b-tabs & {
		background: unset;
		padding-bottom: unset;
		margin-top: var(--spacer-60);
		margin-bottom: var(--spacer-60);

		@media(--viewport-medium) {
			margin-top: var(--spacer-60);
			margin-bottom: var(--spacer-60);
		}

		.b-shortlist__container {
			padding: unset;
		}

		.b-shortlist__content {
			max-width: unset;
		}
	}

	#main-content .item-single--perfect-post-layout &,
	#main-content .b-tabs & {
		& + p {
			margin-top: 0 !important;
		}

		& + :not(.c-block):not(.wp-block-group) {
			margin-top: var(--spacer-60);
		}
	}

	&.--2_lists {
		.b-shortlist__content {
			padding: var(--spacer-50);
			height: max-content;

			.b-shortlist__description {
				max-width: none;

				p {
					font-size: var(--font-size-body-small);
				}
			}

			.c-listicle__item:before {
				width: 30px;
				height: 30px;
			}

			.c-listicle__item-link {
				font-size: var(--font-size-heading-xxsmall);
			}

			.c-listicle__item-usp {
				font-size: var(--font-size-body-xsmall);
			}

			.c-listicle__item-btn-wrapper {
				display: none;
			}

			.b-shortlist__item-image-wrapper {
				@media (--viewport-medium) {
					margin-left: 66px;
				}
			}

			.c-listicle__item-content {
				padding-left: var(--spacer-30);
			}

			.c-listicle__title {
				font-size: var(--font-size-heading-small);
			}
		}
	}

}

.b-shortlist__header {
	text-align: center;

	.b-shortlist .b-shortlist__content & {
		margin-bottom: var(--spacer-30);

		& > .b-shortlist__title,
		& > .b-shortlist__description,
		& > .c-content-block__cta-wrap {
			width: 100%;
			max-width: 100%;
		}

		& > .c-content-block__cta-wrap {
			margin-top: var(--spacer-20);
		}
	}

	.c-content-block__cta-wrap,
	a.c-content-block__cta-link {
		width: 100%;
	}

	@media (--viewport-medium) {
		text-align: left;

		.c-content-block__cta-wrap,
		a.c-content-block__cta-link {
			width: auto;
		}
	}
}

.b-shortlist__content {
	max-width: var(--grid-width-staggered-double);
	margin: 0 auto;
}

.b-shortlist__item {
	padding-top: var(--spacer-20);
	padding-bottom: 0;
	border: 0;

	&:before {
		display: none;
	}
}

.b-shortlist__item-content {
	font-size: var(--font-size-body);
	padding: 0;
	margin: 0;

	span, a {
		font-size: var(--font-size-body-small);
	}
}

.b-shortlist__item-position {
	font-size: var(--font-size-body-small);
	font-weight: var(--font-weight-semibold);
}

.b-shortlist__item-link {
	.t-sink & {
		@mixin t-body-small;

		color: var(--color-primary);
		font-weight: var(--font-weight-bold);
		text-decoration: underline;
	}
}

.b-shortlist__item-usp {
	font-size: var(--font-size-body);
	letter-spacing: .1px;

	.b-shortlist__item--highlighted & {
		font-weight: var(--font-weight-semibold);
	}
}

a.b-shortlist__item-usp-link {
	text-decoration: none;
	color: var(--color-text);
	font-weight: unset;

	&:hover, &:focus, &:active {
		text-decoration: underline;
		color: var(--color-text);
	}
}

.b-shortlist__item--hidden {
	display: none;
}

.b-shortlist__container-tool-card {
	display: flex;

	@media (--viewport-full) {
		column-gap: 54px;
	}

	.b-shortlist__content {
		flex: 1;
		box-shadow: none;
		border-radius: 0;
		background: transparent;
		padding: 0;

		.b-shortlist__description {
			max-width: none;
		}
	}

	.b-shortlist__tool-card {
		display: none;
		width: var(--grid-4-col);
		box-shadow: var(--box-shadow-30);
		border-radius: var(--border-radius-media);
		padding: var(--spacer-70) var(--spacer-50);
		height: max-content;
		background-color: var(--color-white);

		@media (--viewport-full) {
			display: block;
		}

		.c-block__cta-container {
			text-align: center;
		}
	}

	.b-shortlist__image-tool-card {
		margin-bottom: var(--spacer-30);
	}
}

.t-sink > .wp-block-group ol.b-shortlist__items {
	list-style: none;
	padding-left: 0;
}

.b-shortlist__load-button {
	border-left: 0;
	border-right: 0;
	border-top: 0;
	background: transparent;
	padding: var(--spacer-10);

	&-container {
		margin-top: var(--spacer-30);
		height: calc(var(--spacer-60) + var(--spacer-10));

		&--hidden {
			display: none;
		}
	}
}

.c-block__cta-container {
	margin-top: var(--spacer-60);
}

.b-shortlist__featured {
	display: grid;
	gap: var(--spacer-40);
	grid-template-columns: calc(var(--spacer-40) + var(--spacer-50)) 1fr;
	border-top: 1px solid var(--color-neutral-20);
	margin-top: var(--spacer-50);
	padding-top: var(--spacer-60);
	align-items: center;

	@media (--viewport-medium) {
		grid-template-columns: calc(var(--spacer-40) + var(--spacer-50)) 1fr auto;
	}

	&:not(:has(> div:nth-child(3))) {
		grid-template-columns: 1fr auto;

		@media (--viewport-medium-max) {
			grid-template-columns: 1fr;
		}
	}

	@media (--viewport-medium-max) {
		&:not(:has(> div:nth-child(3))) &__cta {
			grid-area: auto;
		}
	}

	&__img {
		&__link {}
		&__img {
			height: calc(var(--spacer-40) + var(--spacer-50));
			width: 100%;
			background-size: contain;
			background-repeat: no-repeat;
			background-position: center;
		}
	}

	&__content {
		@media (--viewport-medium) {
			max-width: 400px;
		}

		&__title {}
		&__description {
			margin-top: var(--spacer-10);
		}
	}

	&__cta {
		grid-area: 2 / 2 / 3 / 3;

		@media (--viewport-medium) {
			grid-area: unset;
		}

		&__link {}
	}
}
