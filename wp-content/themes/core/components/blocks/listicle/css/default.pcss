/* -----------------------------------------------------------------------------
 *
 * Component: Listicle Blocks
 *
 * ----------------------------------------------------------------------------- */
.c-listicle {
	&:first-child {
		&:not(.b-shortlist) {
			padding-top: 80px;

			@media (--viewport-medium) {
				padding-top: 120px;
			}
		}
	}
}

.c-listicle__header:not(.b-shortlist__header) {
	&.c-block__header {
		margin-bottom: var(--spacer-40);

		@media (--viewport-medium) {
			margin-bottom: var(--spacer-50);
		}
	}

	.c-content-block__cta {
		/* CASE: Nested in group block */

		.wp-block-group.has-background & {
			margin-top: var(--spacer-30);

			@media (--viewport-medium) {
				margin-top: var(--spacer-40);
			}
		}
	}
}

.c-listicle__title:not(.b-shortlist__title) {
	@mixin t-display-small;
	letter-spacing: -.1px;

	/* Adjustment to width since this block is less wide than normal blocks */

	.c-block__header.c-block__header--cta-end & {
		padding-right: 0;

		@media (--viewport-full) {
			max-width: 70%;
			padding-right: var(--spacer-20);
		}
	}

	/* CASE: Nested in group block */

	.wp-block-group.has-dark-background-color & {
		color: var(--color-black);
	}

	@media (--viewport-medium) {
		@mixin t-display;
	}
}

.c-listicle__description:not(.b-shortlist__description) {
	/* Adjustment to width since this block is less wide than normal blocks */

	.c-block__header.c-block__header--cta-end & {
		margin-top: var(--spacer-30);
		padding-right: 0;

		@media (--viewport-full) {
			max-width: 70%;
			margin-top: var(--spacer-20);
			padding-right: var(--spacer-20);
		}
	}

	p {
		&:first-child {
			padding-top: 0;
		}

		/* CASE: Nested in group block */

		.wp-block-group.has-dark-background-color & {
			color: var(--color-black);
		}
	}
}

/* -----------------------------------------------------------------------------
 * Listicle List
 * ----------------------------------------------------------------------------- */
.c-listicle__items {
	counter-reset: items-counter;
}

/* -----------------------------------------------------------------------------
 * Listicle Item
 * ----------------------------------------------------------------------------- */
.c-listicle__item {
	position: relative;

	&.b-shortlist__item {
		&:before {
			display: none;
		}
	}

	.s-sink > .wp-block-group & {
		~ li {
			margin-top: 0;
		}
	}
}

.c-listicle__item-content {
	flex: 1 1 auto;
	order: 1;

	&.b-shortlist__item {
		padding-right: 0;
		padding-left: 0;
	}
}

.c-listicle__item-link {
	.t-sink & {
		@mixin t-body-small;

		color: var(--color-primary);

		@media (--viewport-medium) {
			@mixin t-display-x-small;
		}
	}

	/* CASE: Nested in group block */

	.wp-block-group.has-background & {
		color: var(--color-primary);
	}
}

.c-listicle__item-usp {
	@mixin t-helper;

	margin-top: 4px;

	@media (--viewport-medium) {
		@mixin t-body-small;
	}

	/* CASE: Nested in group block */

	.wp-block-group.has-background & {
		color: var(--color-black);

		@media (--viewport-medium) {
			@mixin t-body-small;
		}
	}
}

.c-listicle__item-btn-wrapper {
	display: none;

	@media (--viewport-full) {
		flex: 0 0 auto;
		display: block;
		order: 3;
		margin-left: var(--spacer-60);
	}
}
