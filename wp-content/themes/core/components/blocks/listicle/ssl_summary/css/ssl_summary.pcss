/* -----------------------------------------------------------------------------
 *
 * Component: Block: SSL Summary
 *
 * ----------------------------------------------------------------------------- */

.b-ssl-summary__content {
	.b-ssl-summary-table__ppl-link-container {
		display: none;
	}

	&--has-ppl-links {
		.b-ssl-summary-table__ppl-link-container {
			display: block;
			margin-top: var(--spacer-10);
			height: calc(var(--font-size-body-large) + 8px);

			.a-cta--secondary {
				display: inline-block;
				white-space: nowrap;

				@media (--viewport-medium-max) {
					display: table;
				}
			}
		}
	}
}

.b-ssl-summary-table {
	position: relative;
	z-index: 1;

	.item-single--post & {
		z-index: 3;
	}
}

.b-ssl-summary-table {
	+ .b-shortlist {
		margin-top: -80px;

		@media (--viewport-medium) {
			margin-top: -120px;
		}
	}
}

.b-ssl-summary-table__header {
	padding-left: var(--spacer-30);
	padding-right: var(--spacer-30);
	margin-bottom: var(--spacer-50);

	&.c-content-block {
		max-width: 100%;
	}

	@media (--viewport-full) {
		padding-left: 120px;
		padding-right: 120px;
		margin-bottom: var(--spacer-60);
	}
}

.b-ssl-summary-table__title,
.b-ssl-summary-table__description {
	max-width: 940px;
}

/* -----------------------------------------------------------------------------
 * Table
 * ----------------------------------------------------------------------------- */
.b-ssl-summary-table__table {
	position: relative;

	&:before {
		@media (--viewport-large) {
			display: block;
			content: '';
			position: absolute;
			top: calc(var(--spacer-60) * -1);
			left: 0;
			right: 0;
			height: var(--spacer-60);
			z-index: 10;
			user-select: none;
			pointer-events: none;
			background: rgb(255, 255, 255);
			background: linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
			width: 100%;
		}
	}

	&:after {
		@media (--viewport-large) {
			display: block;
			content: '';
			position: absolute;
			bottom: calc(var(--spacer-60) * -1);
			left: 0;
			right: 0;
			height: var(--spacer-60);
			z-index: 10;
			user-select: none;
			pointer-events: none;
			background: rgb(255, 255, 255);
			background: linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
			width: 100%;
		}
	}

	.t-sink &:not(.acf-table) {
		width: 100%;
		background-color: var(--color-white);
		border-left: 0;
		border-right: 0;

		th ~ th, td:first-child {
			border-left: 0;
		}

		@media (--viewport-small-max) {
			margin: 0 calc(var(--grid-margin-small) * -1);
			width: 100vw;
		}

		@media (--viewport-medium-max) {
			td {
				display: block;
				padding: var(--spacer-20) var(--spacer-40);

				&:first-child {
					position: absolute;
					z-index: 0;
				}

				&:nth-child(2) {
					position: relative;
					z-index: 1;
				}

				&:before {
					@mixin t-label;

					content: attr(data-th);
					display: block;
					margin-bottom: var(--spacer-10);
					text-transform: uppercase;
					font-weight: var(--font-weight-bold);
					color: var(--color-neutral-50);
					letter-spacing: 1px;
				}
			}

			tr {
				padding: var(--spacer-20) 0;
			}
		}
	}

	.t-sink & {
		table-layout: fixed;
		border: 0;
	}

	thead {
		border-top: 1px solid var(--color-neutral-20);
		border-bottom: 1px solid var(--color-neutral-20);
		background-color: var(--color-white);
		z-index: 2;
		box-shadow: 0 1px 0 var(--color-neutral-20);

		@media (--viewport-medium-max) {
			display: none;
		}
	}

	tbody {
		tr {
			.t-sink & {
				~ tr {
					border-top: 1px solid var(--color-neutral-20);
				}
			}

			&:last-child {
				border-bottom: 1px solid var(--color-neutral-20);
			}

			@media (--viewport-medium-max) {
				display: block;
			}
		}
	}

	th {
		@mixin t-overline;

		.t-sink & {
			padding-top: var(--spacer-30);
			padding-bottom: var(--spacer-30);

			~ th {
				border-left: 0;
			}
		}
	}

	td {
		@mixin t-body-small;

		.t-sink & {
			padding-top: var(--spacer-50);
			padding-bottom: var(--spacer-50);

			~ td {
				border-left: 0;
			}
		}
	}
}

.admin-bar .b-ssl-summary-table__table thead {
	top: 120px;
}

.wp-admin .b-ssl-summary-table__table thead {
	top: 0;
}

/* -----------------------------------------------------------------------------
 * Table Columns
 * ----------------------------------------------------------------------------- */
.b-ssl-summary-table__col-index {
	@media (--viewport-full) {
		width: 90px;
	}
}

.b-ssl-summary-table__col-product {
	word-break: break-word;

	@media (--viewport-full) {
		width: 35%;

		.t-sink & {
			padding-left: 0;
		}
	}

	@media (--viewport-large) {
		width: auto;
	}
}

.b-ssl-summary-table__col-free {
	@media (--viewport-full) {
		width: 150px;

		.t-sink & {
			padding-left: 0;
		}

		&:before {
			content: '';
		}
	}

	@media (--viewport-large) {
		width: 13%;
	}
}

.b-ssl-summary-table__col-price {
	@media (--viewport-full) {
		width: 150px;

		.t-sink & {
			padding-left: 0;
		}

		&:before {
			content: '';
		}
	}

	@media (--viewport-large) {
		width: 13%;
	}
}


.b-ssl-summary-table__col-cta {
	@media (--viewport-medium-max) {
		display: none;
	}

	@media (--viewport-full) {
		text-align: center;

		.t-sink & {
			padding-right: 30px;
			padding-left: 0;
		}
	}

	@media (--viewport-large) {
		width: 160px;

		.t-sink & {
			padding-right: 120px;
			padding-left: 0;
		}
	}
}

/* -----------------------------------------------------------------------------
 * Table Elements
 * ----------------------------------------------------------------------------- */
.b-ssl-summary-table__product-index {
	display: flex;
	justify-content: center;
	align-items: center;

	span {
		display: flex;
		justify-content: center;
		align-items: center;

		@mixin t-label;

		border: 2px solid var(--color-background-dark);
		color: var(--color-background-dark);
		text-align: center;
		border-radius: 4px;
		font-weight: var(--font-weight-bold);
		line-height: 1.5;

		@media (--viewport-full) {
			@mixin t-display-x-small;

			top: calc(50% - 20px);
			width: 40px;
			height: 40px;
			border-radius: 8px;
			border: 3px solid var(--color-background-dark);
			line-height: 1.5;
		}
	}

	@media (--viewport-medium-max) {
		height: 100%;

		span {
			width: 24px;
			height: 24px;
		}
	}
}

.b-ssl-summary-table__col-product-flex {
	display: flex;
	flex-wrap: nowrap;
	align-items: center;

	@media (--viewport-large-max) {
		gap: var(--spacer-20);
		flex-wrap: wrap;
	}

	@media (--viewport-medium-max) {
		justify-content: space-between;
		flex-wrap: nowrap;
	}
}

.b-ssl-summary-table__product-logo {
	flex: 0 0 64px;

	@media (--viewport-medium-max) {
		order: 2;
		height: 64px;

		.c-image__image {
			width: 64px;
			max-height: 64px;
		}
	}
}

.b-ssl-summary-table__product-details {
	display: flex;
	flex-wrap: wrap;
	padding-left: var(--spacer-40);

	@media (--viewport-medium-max) {
		order: 1;
		padding-right: var(--spacer-30);
	}

	@media (--viewport-large-max) {
		padding-left: 0;
	}
}

.b-ssl-summary-table__product-title {
	word-break: keep-all;
	font-size: var(--font-size-body);

	.t-sink & {
		color: var(--color-primary);
		font-size: var(--font-size-body);
	}

	@media (--viewport-medium-max) {
		@mixin t-body-small;

		padding-left: var(--spacer-40);
		margin-bottom: var(--spacer-10);
	}

	@media (--viewport-full) {
		@mixin t-display-x-small;

		flex: 0 1 auto;
	}
}

.b-ssl-summary-table__product-usp {
	flex: 1 0 100%;
}

.b-ssl-summary-table__product-btn {
	.t-sink & {
		&.a-btn {
			max-width: 250px;
			text-align: center;
			padding-left: var(--spacer-20);
			padding-right: var(--spacer-20);
		}
	}
}

.item-single--perfect-listicle-layout,
.item-single--perfect-post-layout {
	.b-ssl-summary-table {
		max-width: var(--grid-8-col);
	}

	.t-sink .b-ssl-summary-table__table:not(.acf-table) {
		@media (--viewport-large) {
			width: calc(100% + var(--grid-sidebar-col) + var(--spacer-80));
			max-width: calc(var(--grid-max-width) - calc(var(--grid-margin) * 2));
		}
	}
}

#main-content .item-single--perfect-listicle-layout,
#main-content .item-single--perfect-post-layout {
	.c-block.b-ssl-summary-table {
		@media (--viewport-large) {
			max-width: var(--grid-8-col);
		}
	}

	.item-single__content-container .b-shortlist__description {
		margin-top: var(--spacer-20);
	}
}
