/* -----------------------------------------------------------------------------
 *
 * Component: Block: Full Listing
 *
 * ----------------------------------------------------------------------------- */
.b-full-listing {
	&:first-child {
		margin-bottom: 0;

		@media(--viewport-medium) {
			margin-bottom: 0;
		}
	}

	& + :not(.c-block):not(.wp-block-group) {
		margin-top: 80px;

		@media(--viewport-medium) {
			margin-top: 120px;
		}
	}

	+ .b-shortlist {
		margin-top: -80px;

		@media(--viewport-medium) {
			margin-top: -120px;
		}
	}
}

.b-full-listing__header {
	&.c-listicle__header.c-block__header {
		margin-bottom: var(--spacer-30);

		@media (--viewport-medium) {
			margin-bottom: var(--spacer-70);
		}
	}
}

/* -----------------------------------------------------------------------------
 * Full Listing Item
 * ----------------------------------------------------------------------------- */
.b-full-listing__item {
	&:not(:only-child) {
		padding-bottom: var(--spacing-block-mobile-default);
	}

	&:not(:first-child) {
		padding-top: var(--spacing-block-mobile-default);
	}

	&:nth-child(2n) {
		background-color: var(--color-neutral-10);
	}

	@media (--viewport-full) {
		&:not(:only-child) {
			padding-bottom: var(--spacing-block-desktop-default);
		}

		&:not(:first-child) {
			padding-top: var(--spacing-block-desktop-default);
		}
	}

	&__release-note {
		order: 12;
    	margin-top: var(--spacer-60);

		&.s-sink, .s-sink {
			> h4 {
				margin-bottom: var(--spacer-40);
			}

			> h5 {
				margin-top: 0;
				margin-bottom: var(--spacer-20);
				font-weight: var(--font-weight-semibold);

				&:not(:first-child) {
					margin-top: var(--spacer-10);
				}
			}

			p {
				font-size: var(--font-size-body-small);
			}

			ol, ul {
				padding-left: var(--spacer-20);
				font-size: var(--font-size-body-small);
			}
		}

		.t-overline {
			color: var(--color-neutral-50);
		}

		&__container {
			display: flex;
			gap: var(--spacer-40);
			width: 100%;
			flex-direction: column-reverse;

			@media (--viewport-medium) {
				flex-direction: row;
			}

			figcaption {
				border: none;
				padding-bottom: 0;
				margin-bottom: 0;
				text-align: center;
				font-style: italic;
			}

			img {
				.b-accordion-advanced__item-bd:has(:not(figure)) & {
					margin-bottom: 0;
				}
			}
		}

		&__column {
			&:first-child:not(:only-child) {
				width: 100%;

				@media (--viewport-medium) {
					flex-shrink: 0;
					width: 330px;
				}
			}
		}
	}
}

.b-full-listing__item-container {
	display: flex;
	flex-flow: row wrap;
}

/* -----------------------------------------------------------------------------
 * Full Listing Item Header
 * ----------------------------------------------------------------------------- */
.b-full-listing__item-header {
	flex: 0 0 100%;
	order: 1;
	margin-bottom: var(--spacer-40);

	@media (--viewport-full) {
		display: grid;
		grid-template-columns: 54px 1fr;
		grid-template-rows: auto;
		grid-column-gap: var(--spacer-40);
	}

	@media(--viewport-full) {
		padding-right: var(--spacer-80);
	}
}

.b-full-listing__item-counter {
	@mixin t-display-small;

	display: flex;
	justify-content: center;
	align-items: center;
	width: 54px;
	height: 52px;
	border: 3px solid var(--color-background-dark);
	color: var(--color-background-dark);
	text-align: center;
	border-radius: 8px;
	margin-bottom: var(--spacer-20);

	@media (--viewport-full) {
		grid-column: 1/1;
		grid-row: 1/2;
		margin-bottom: 0;
	}
}

.b-full-listing__item-title {
	@media (--viewport-full) {
		grid-column: 2/2;
		grid-row: 1/1;
	}
}

.b-full-listing__item-usp {
	@mixin t-body;

	@media (--viewport-full) {
		grid-column: 2/2;
		grid-row: 2/2;
		margin-bottom: var(--spacer-40);
	}
}

/* -----------------------------------------------------------------------------
 * Full Listing Item Content
 * ----------------------------------------------------------------------------- */
.b-full-listing__item-content {
	flex: 0 0 100%;
	order: 3;

	@media (--viewport-full) {
		flex: 0 1 calc(100% - var(--grid-4-col));
		order: 2;
		padding-right: var(--spacer-80);
	}
}

.b-full-listing__item-description {
	.t-sink & {
		> :first-child {
			padding-top: 0;
			margin-top: 0;
		}
	}
}

.b-full-listing__item-extra {
	margin-top: var(--spacer-60);
}

.b-full-listing__item-content-links {
	margin-top: var(--spacer-40);
	margin-bottom: var(--spacer-40);

	&__subtitle {
		margin-bottom: var(--spacer-20);
	}

	& &__list-item-link {
		&--secondary {
			font-weight: var(--font-weight-regular);

			&:hover, &:focus, &:active {
				color: var(--color-primary-70);
			}
		}
	}

	&__list-item {
		margin-bottom: var(--spacer-10);
	}
}

/* -----------------------------------------------------------------------------
 * Full Listing Item Sidebar
 * ----------------------------------------------------------------------------- */
.b-full-listing__item-sidebar {
	flex: 0 0 100%;
	order: 2;
	position: relative;

	@media (--viewport-full) {
		flex: 0 0 var(--grid-4-col);
		order: 3;
		margin-bottom: var(--spacer-70);
	}
}

.b-full-listing__item-sidebar-container {
	@media (--viewport-full) {
		position: sticky;
		top: 0;
	}
}

.b-full-listing__item-sidebar-cta-box {
	background-color: var(--color-white);
	box-shadow: var(--box-shadow-30);
	border-radius: var(--border-radius-media);
	padding: var(--spacer-40) var(--spacer-30);
	margin-bottom: var(--spacer-40);

	@media (--viewport-full) {
		padding: var(--spacer-50);
	}
}

.b-full-listing__item-logo {
	padding-left: var(--spacer-30);
	padding-right: var(--spacer-30);
	margin-bottom: var(--spacer-30);

	@media (--viewport-full) {
		padding-left: 0;
		padding-right: 0;
		margin-bottom: var(--spacer-40);
	}
}

.b-full-listing__item-logo-img {
	.t-sink & {
		max-width: 200px;
		height: auto;
		border-radius: 0;
	}
}

.b-full-listing__item-score {
	position: relative;
	display: flex;
	justify-content: center;

	&-container {
		margin-bottom: var(--spacer-40);
		max-width: 280px;
		margin-left: auto;
		margin-right: auto;

		display: inline-grid;
		grid-template-columns: repeat(4, auto);
		justify-content: center;
		align-items: baseline;
		gap: var(--spacer-10);

		user-select: none;
	}

	&__score {
		@mixin t-body;

		font-weight: var(--font-weight-semibold);
		line-height: 1;
	}

	&__stars {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		justify-content: center;
		align-items: center;
		fill: var(--color-secondary);

		svg {
			width: var(--spacer-20);
			height: var(--spacer-20);
		}
	}

	&__votes {
		@mixin t-body-small;

		color: #585858;
		line-height: 1;
	}

	&__info {
		width: var(--font-size-body-small);
		height: var(--font-size-body-small);

		&::before {
			content: var(--icon-info-outlined);
			font-family: var(--font-family-core-icons);
			display: flex;
			align-items: center;
			justify-content: center;
			width: var(--font-size-body-small);
			height: var(--font-size-body-small);
			background-color: var(--color-neutral-10);
			margin-right: var(--spacer-30);
			color: var(--color-grey);
			font-size: var(--font-size-body-small);
			border-radius: 50%;
			border: 1px solid var(--color-grey);
			box-sizing: border-box;
		}
	}
}

.b-full-listing__item-free-options {
	margin-bottom: var(--spacer-30);
	max-width: 280px;
	margin-left: auto;
	margin-right: auto;

	p {
		@mixin t-body-small;

		display: flex;
		align-items: flex-start;

		&:before {
			@mixin icon;

			display: inline-block;
			flex: 0 0 var(--spacer-40);
			content: var(--icon-check);
			color: var(--color-primary);
			font-size: var(--font-size-body);
		}

		&:not(:last-of-type) {
			margin-bottom: var(--spacer-20);
		}
	}

	@media (--viewport-full) {
		margin-bottom: var(--spacer-40);
	}
}

.b-full-listing__item-btn-wrapper {
	display: flex;
	justify-content: center;
	align-items: center;
	flex: 1 0 100%;
	order: 6;
	max-width: 340px;
	margin-left: auto;
	margin-right: auto;

	@media (--viewport-full) {
		order: unset;
	}
}

a.b-full-listing__item-btn {
	.t-sink & {
		text-align: center;

		@media (--viewport-full) {
			flex: 0 1 auto;
		}
	}
}

a.b-full-listing__item-btn--mobile {
	margin-top: var(--spacer-50);
	margin-bottom: 0;
	display: block;

	.t-sink & {
		@media (--viewport-full) {
			display: none;
		}
	}
}

/* -----------------------------------------------------------------------------
 * Full Listing Item Additional Links
 * ----------------------------------------------------------------------------- */
.b-full-listing__item-additional-links {
	margin-top: var(--spacer-40);
	text-align: center;
	width: 100%;

	&.b-full-listing__item-additional-links--mobile {
		display: block;
		order: 5;
		margin-top: var(--spacer-50);

		@media (--viewport-full) {
			display: none;
		}
	}

	&.b-full-listing__item-additional-links--desktop {
		display: none;

		@media (--viewport-full) {
			display: block;
		}
	}
}

.b-full-listing__item-additional-links-title {
	@mixin t-overline;

	margin-bottom: var(--spacer-30);
}

.b-full-listing__item-additional-links-list {
	li {
		margin-bottom: var(--spacer-30);

		&:last-child {
			margin-bottom: 0;
		}
	}
}

.b-full-listing__item-additional-links-link {
	@mixin t-body-small;

	.t-sink & {
		font-weight: var(--font-weight-regular);
	}
}

/* -----------------------------------------------------------------------------
 * Full Listing in Perfect Listicle Layout
 * ----------------------------------------------------------------------------- */
.item-single--perfect-listicle-layout {
	.b-full-listing {
		.b-full-listing {
			&__header.c-block__header--cta-end {
				max-width: 100%;
				align-items: flex-start;
				flex-direction: column;

				.b-full_listing__title, .b-full_listing__description {
					max-width: 100%;
				}
			}

			&__item {
				&:nth-child(2n) {
					background: unset;
				}

				&:not(:first-child) {
					border-top: 1px solid var(--color-neutral-20);
					padding-top: var(--spacer-40);

					@media (--viewport-full) {
						padding-top: var(--spacer-70);
					}
				}

				&:not(:only-child) {
					padding-bottom: var(--spacer-30);

					@media (--viewport-full) {
						padding-bottom: var(--spacer-70);
					}
				}

				&-header {
					&.b-full-listing__item-header--has-image {
						@media (--viewport-full) {
							grid-template-columns: 128px auto;
							padding-right: 0;
							grid-column-gap: var(--spacer-30);
							align-items: flex-start;
						}
					}

					&:not(.b-full-listing__item-header--has-image) {
						display: block;
					}
				}

				&-counter {
					width: var(--spacer-50);
					height: var(--spacer-50);
					border-width: 3px;

					@media (--viewport-full) {
						@mixin t-display-small;
						font-weight: var(--font-weight-semibold);
						line-height: 0;
					}
				}

				&-tool-title {
					margin-bottom: var(--spacer-20);

					@media (--viewport-full) {
						display: grid;
						grid-template-columns: 40px auto;
						grid-column-gap: var(--spacer-20);
						align-items: center;
					}
				}

				&-title {
					@media (--viewport-full) {
						display: inline;
					}

					&-link {
						&:hover {
							color: var(--color-primary-70);
							text-underline-offset: 5px;
							text-decoration-thickness: 4px;
						}

						h3 {
							font-weight: var(--font-weight-semibold);
						}
					}
				}

				&-usp {
					margin-bottom: var(--spacer-20);

					@media (--viewport-full) {
						margin-bottom: 0;
					}
				}

				&-header-btn-wrapper {
					display: flex;
    				align-items: center;

					@media (--viewport-small-max) {
						justify-content: space-between;
					}

					.b-full-listing__item-btn {
						padding: var(--spacer-20) var(--spacer-50);

						@media (--viewport-xsmall-max) {
							padding: var(--spacer-20) var(--spacer-40);
						}
					}

					.b-full-listing__item-info-box {
						margin-left: var(--spacer-30);
						font-size: 18px;

						@media (--viewport-xsmall-max) {
							margin-left: var(--spacer-10);
							font-size: var(--font-size-body-small);
						}
					}
				}

				&-btn-wrapper {
					margin-top: var(--spacer-60);
					margin-bottom: var(--spacer-60);
					order: 6;
				}

				&-content {
					order: 3;
					flex: 1 1 100%;

					@media (--viewport-full) {
						padding-right: 0;
					}

					.c-screenshot-gallery__item-figure {
						width: 100%;
						min-height: 210px;

						@media (--viewport-xsmall) {
							min-height: 370px;
						}

						@media (--viewport-medium) {
							min-height: 380px;
						}

						@media (--viewport-full) {
							min-height: 500px;
						}

						&:has( [data-ll-status="loaded"] ) {
							min-height: fit-content;
						}
					}
				}

				&-info {
					&--with-score {
						grid-template-areas: "logo score" "description description";
						grid-template-columns: 128px 1fr;

						@media (--viewport-full) {
							grid-template-areas: "logo score description";
							grid-template-columns: 128px 1fr 2fr;
						}
					}

					&-box {
						list-style: disc inside;
					}
				}

				&-logo {
					margin: 0;
					padding: 0;
					overflow: hidden;
					border-radius: var(--border-radius-base);
					border: 1px solid var(--color-neutral-20);

					&-container {
						@media (--viewport-medium-max) {
							display: none;
						}
					}

					&-img {
						padding: var(--spacer-10);
						height: auto;
						width: 128px;
						min-height: 128px;
					}
				}

				&-score {
					width: 100%;
					flex-direction: column;
					align-items: flex-start;
					gap: var(--spacer-10);
					grid-area: score;

					&-container {
						grid-template-columns: repeat(2, auto);
						margin: unset;
						max-width: 100%;

						.c-tooltip {
							left: 0;
							margin: 0;
							transform: translateX(-80px);

							@media (--viewport-xsmall) {
								transform: translateX(-14px);
							}
						}
					}

					&__stars {
						gap: calc(var(--spacer-10) / 2);
					}

					&__score {
						@media (--viewport-xsmall-max) {
							font-size: var(--font-size-body-small);
						}
					}
				}

				&-free-options {
					margin: var(--spacer-20) 0 var(--spacer-30);
					max-width: 100%;

					@media (--viewport-medium) {
						margin: var(--spacer-30) 0 var(--spacer-30);
					}
				}

				&-info-box {
					li ~ li {
						margin-top: var(--spacer-10);
					}
				}

				&-extra {
					order: 8;
					flex: 1 1 100%;

					&-tabs {
						display: flex;
						flex-wrap: nowrap;
						align-items: center;
						justify-content: flex-start;
						column-gap: var(--spacer-50);
						position: absolute;
						width: 100%;

						@media (--viewport-medium) {
							border-bottom: 1px solid var(--color-neutral-20);
						}

						&-container {
							overflow-x: scroll;
							overflow-y: hidden;
							width: 100%;
							height: 60px;
							position: relative;

							@mixin scrollbar-neutral;

							@media (--viewport-medium) {
								overflow-x: auto;
							}

							&::-webkit-scrollbar-track {
								background-color: var(--color-neutral-10);
							}
						}
					}

					&-tab {
						@mixin t-body-small;
						font-weight: var(--font-weight-semibold);
						color: var(--color-text);
						white-space: nowrap;

						display: block;
						padding-top: var(--spacer-10);
						padding-bottom: var(--spacer-30);

						&.active {
							color: var(--color-primary-50);
							position: relative;

							&:after {
								display: block;
								content: "";
								position: absolute;
								width: 100%;
								height: var(--spacer-10);
								background: var(--color-primary-50);
								bottom: 0;

								@media (--viewport-medium) {
									bottom: calc(calc(var(--spacer-10) / 2) * -1);
								}
							}
						}

						&:hover, &:focus, &:active {
							color: var(--color-primary-70);
							text-decoration: none;
						}
					}

					&-content {
						margin-top: var(--spacer-50);
					}

					&__procon {
						@media (--viewport-full) {
							display: grid;
							grid-template-columns: 1fr 1fr;
							grid-gap: var(--spacer-40);
						}
					}
				}

				&-procon {
					padding: 0;
					background-color: unset;
					display: grid;
					grid-template-columns: 1fr;
					grid-gap: var(--spacer-30);
					margin-top: 0;

					@media (--viewport-medium) {
						grid-template-columns: 1fr 1fr;
						grid-gap: var(--spacer-40);
					}

					&-label {
						margin-bottom: var(--spacer-20);
						margin-top: var(--spacer-20);

						@mixin t-body-small;
						font-weight: var(--font-weight-semibold);

						@media (--viewport-full) {
							margin-top: var(--spacer-40);

							@mixin t-body;
							font-weight: var(--font-weight-semibold);
						}
					}

					&-list {
						list-style: disc outside;
					}

					&-item {
						@mixin t-body-small;

						@media (--viewport-full) {
							@mixin t-body;
						}

						margin-left: var(--spacer-30);

						&:not(:last-child) {
							margin-bottom: var(--spacer-20);
						}
					}
				}

				&-pros, &-cons {
					margin: 0;
				}
			}
		}

		& + :not(.c-block):not(.wp-block-group) {
			margin-top: var(--spacer-70);
		}
	}
}
