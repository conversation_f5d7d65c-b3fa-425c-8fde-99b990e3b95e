/* -----------------------------------------------------------------------------
 *
 * Component: Blocks
 *
 * ----------------------------------------------------------------------------- */

/* -----------------------------------------------------------------------------
 * Global: Group Block (used as a wrapper to apply background color to blocks)
 * ----------------------------------------------------------------------------- */
.wp-block-group {

	.l-sink > & {
		max-width: 100%;
		width: 100%;
		padding-top: 80px;
		padding-bottom: 80px;

		@media (--viewport-full) {
			padding-top: 120px;
			padding-bottom: 120px;
		}

		&.has-light-background-color {
			background-color: var(--color-background-light);
		}

		&.has-dark-background-color {
			background-color: var(--color-background-dark);
			color: var(--color-white);

			* {
				color: var(--color-white);
			}
		}

		&.has-background {
			& ~ p,
			& ~ .b-buttons {
				margin-top: var(--spacer-30);
			}
		}

		&.has-background-image {
			background-repeat: no-repeat;
			background-size: cover;
			background-position: center;
		}
	}
}

/* -----------------------------------------------------------------------------
 * Global: Block
 * ----------------------------------------------------------------------------- */

.c-block {
	margin-top: 80px;
	margin-bottom: 80px;

	@media (--viewport-medium) {
		margin-top: 120px;
		margin-bottom: 120px;
	}

	/* CASE: Block is full-bleed */
	&.c-block--full-bleed {
		max-width: none;
		width: 100%;
	}
}

/* -----------------------------------------------------------------------------
 * CASE: If a full-bleed block is followed by a non-component-block element,
 * the following element should have some additional vertical spacing.
 * ----------------------------------------------------------------------------- */
.c-block--full-bleed + *:not(.c-block) {
	margin-top: var(--spacer-60);

	@media (--viewport-medium) {
		margin-top: var(--spacer-80);
	}
}

/* -----------------------------------------------------------------------------
 * CASE: If a non-component-block element is followed by a full-bleed block,
 * the block should have some additional vertical spacing.
 * ----------------------------------------------------------------------------- */
*:not(.c-block) + .c-block--full-bleed {
	margin-top: var(--spacer-60);

	@media (--viewport-medium) {
		margin-top: var(--spacer-80);
	}
}

/* -----------------------------------------------------------------------------
 * CASE: Collapse spacing between any full-bleed or background-applied blocks.
 * ----------------------------------------------------------------------------- */
.c-block--full-bleed + .c-block--full-bleed,
.c-block--full-bleed + .c-block--vertical-padding,
.c-block--vertical-padding + .c-block--full-bleed,
.c-block--vertical-padding + .c-block--vertical-padding {
	margin-top: calc(-1 * var(--spacer-60));

	@media (--viewport-medium) {
		margin-top: calc(-1 * var(--spacer-80));
	}
}

/* -----------------------------------------------------------------------------
 * CASE: Block has vertical padding
 *
 * Provides vertical spacing in addition to any vertical margin
 * applied between blocks. Different from a full-bleed block in that a
 * full-bleed block generally has custom vertical padding.
 *
 * Useful when applying background colors to blocks, or "zebra-striping".
 * ----------------------------------------------------------------------------- */
.c-block--vertical-padding {
	padding-top: var(--spacer-60);
	padding-bottom: var(--spacer-60);

	@media (--viewport-medium) {
		padding-top: var(--spacer-80);
		padding-bottom: var(--spacer-80);
	}
}

/* -----------------------------------------------------------------------------
 * Global: Header / Content Block
 * ----------------------------------------------------------------------------- */

.c-block__content-block {

}

.c-block__header {
	/* Common header block with right aligned cta */
	&.c-block__header--cta-end {
		align-self: center;
		align-items: flex-end;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		margin-bottom: var(--spacer-50);
		max-width: none;

		.c-block__title,
		.c-block__description {
			max-width: var(--grid-9-col);
			padding-right: var(--spacer-20);
			width: 100%;
		}
	}
}

/* -----------------------------------------------------------------------------
 * Global: Title
 * ----------------------------------------------------------------------------- */

.c-block__title {
	@mixin t-display-small;
	letter-spacing: -.2px;
	overflow-wrap: break-word;

	@media (--viewport-full) {
		@mixin t-display-large;

		/* Fix for group block since h3 class is not matching block title display on all viewports */
		.t-sink .wp-block-group & {
			@mixin t-display-large;
			letter-spacing: -.2px;
		}
	}
}

/* -----------------------------------------------------------------------------
 * Global: Description
 * ----------------------------------------------------------------------------- */

.c-block__description {

}

/* -----------------------------------------------------------------------------
 * Global: CTA
 * ----------------------------------------------------------------------------- */

.c-block__cta {

}

.c-block__cta-link {
	.acf-block-preview & {
		pointer-events: none;
	}
}
