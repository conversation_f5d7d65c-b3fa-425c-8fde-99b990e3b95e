<?php

namespace Tribe\Project\Theme_Customizer;

use Tribe\Project\Theme_Customizer\Customizer_Sections\Colors;
use Tribe\Project\Theme_Customizer\Customizer_Sections\Fonts;
use Tribe\Project\Theme_Customizer\Customizer_Sections\Footer;
use Tribe\Project\Theme_Customizer\Customizer_Sections\Header;
use Tribe\Project\Theme_Customizer\Customizer_Sections\Site_Identity;
use Tribe\Project\Theme_Customizer\Customizer_Sections\Social_Links;

class Customizer_Loader {
	/**
	 * Load all theme customizer controls that are relevant to the current theme.
	 *
	 * @param \WP_Customize_Manager $wp_customize
	 *
	 * @return void
	 * @action customize_register
	 */
	public function register_customizer_controls( \WP_Customize_Manager $wp_customize ) {
		$this->font_customizer_settings( $wp_customize );
		$this->color_customizer_settings( $wp_customize );
		$this->social_customizer_settings( $wp_customize );
		$this->header_customizer_settings( $wp_customize );
		$this->footer_customizer_settings( $wp_customize );
		$this->site_identity_customizer_settings( $wp_customize );
	}

	/**
	 * Creates the font options in the customizer.
	 *
	 * @param \WP_Customize_Manager $wp_customize
	 * @return void
	 */
	protected function font_customizer_settings( \WP_Customize_Manager $wp_customize ) {
		$fonts = new Fonts( $wp_customize );
		$fonts->section_title();
		$fonts->primary_font();
		$fonts->primary_font_color();
		$fonts->secondary_font();
		$fonts->secondary_font_color();
		$fonts->heading_xxlarge_weight();
		$fonts->heading_xlarge_weight();
		$fonts->heading_large_weight();
		$fonts->heading_weight();
		$fonts->heading_small_weight();
		$fonts->heading_xsmall_weight();
		$fonts->heading_xxsmall_weight();
		$fonts->heading_xxxsmall_weight();
		$fonts->heading_xxlarge_font_size();
		$fonts->heading_xlarge_font_size();
		$fonts->heading_large_font_size();
		$fonts->heading_font_size();
		$fonts->heading_small_font_size();
		$fonts->heading_xsmall_font_size();
		$fonts->heading_xxsmall_font_size();
		$fonts->heading_xxxsmall_font_size();
		$fonts->heading_xxlarge_line_height();
		$fonts->heading_xlarge_line_height();
		$fonts->heading_large_line_height();
		$fonts->heading_line_height();
		$fonts->heading_small_line_height();
		$fonts->heading_xsmall_line_height();
		$fonts->heading_xxsmall_line_height();
		$fonts->heading_xxxsmall_line_height();
	}

	/**
	 * Creates color picker settings.
	 *
	 * @param \WP_Customize_Manager $wp_customize
	 * @return void
	 */
	protected function color_customizer_settings( \WP_Customize_Manager $wp_customize ) {
		$colors = new Colors( $wp_customize );
		$colors->section_title();
		$colors->primary_color();
		$colors->primary_focus_color();
		$colors->secondary_color();
		$colors->secondary_focus_color();
		$colors->primary_color_in_dark_bg();
		$colors->primary_focus_color_in_dark_bg();
		$colors->button_primary_font_color();
		$colors->button_primary_font_focus_color();
		$colors->button_primary_font_color_in_dark_bg();
		$colors->button_primary_font_focus_color_in_dark_bg();
		$colors->light_background_color();
		$colors->dark_background_color();
		$colors->positive_alert_color();
		$colors->negitive_alert_color();
		$colors->neutral_alert_color();
		$colors->text_selection_color();
		$colors->highlight_background_color();
		$colors->highlight_text_color();
	}

	/**
	 * Social Media Customizer Settings
	 *
	 * @param \WP_Customize_Manager $wp_customize
	 * @return void
	 */
	protected function social_customizer_settings( \WP_Customize_Manager $wp_customize ) {
		$menu = new Social_Links( $wp_customize );
		$menu->section_title();
		$menu->all_links();
	}

	/**
	 * Header Customizer Settings
	 *
	 * @param \WP_Customize_Manager $wp_customize
	 * @return void
	 */
	protected function header_customizer_settings( \WP_Customize_Manager $wp_customize ) {
		$menu = new Header( $wp_customize );
		$menu->section_title();
		$menu->nav_logo();
		$menu->nav_logo_width();
		$menu->nav_cta_button();
	}

	/**
	 * Header Customizer Settings
	 *
	 * @param \WP_Customize_Manager $wp_customize
	 * @return void
	 */
	protected function site_identity_customizer_settings( \WP_Customize_Manager $wp_customize ) {
		$menu = new Site_Identity( $wp_customize );
		$menu->button_style();
		$menu->button_text_transform();
		$menu->media_style();
		$menu->google_tag_manager();
	}

	/**
	 * Footer Customizer Settings
	 *
	 * @param \WP_Customize_Manager $wp_customize
	 * @return void
	 */
	protected function footer_customizer_settings( \WP_Customize_Manager $wp_customize ) {
		$footer = new Footer( $wp_customize );
		$footer->section_title();
		$footer->set_footer_fields();
	}
}
