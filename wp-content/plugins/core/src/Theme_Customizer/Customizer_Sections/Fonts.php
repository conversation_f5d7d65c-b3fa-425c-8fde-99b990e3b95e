<?php
declare( strict_types=1 );

namespace Tribe\Project\Theme_Customizer\Customizer_Sections;

use Tribe\Project\Theme\Config\Web_Fonts;
use Tribe\Project\Theme_Customizer\Controls\Tribe_Color_Picker;

class Fonts extends Abstract_Setting {
	public const FONTS_SECTION                = 'fonts_section';
	public const PRIMARY_FONT                 = 'primary_font';
	public const PRIMARY_FONT_COLOR           = 'primary_font_color';
	public const PRIMARY_FONT_COLOR_DEFAULT   = '#000000';
	public const SECONDARY_FONT               = 'secondary_font';
	public const SECONDARY_FONT_COLOR         = 'seconday_font_color';
	public const SECONDARY_FONT_COLOR_DEFAULT = '#000000';

	public const HEADING_XXLARGE_FONT_SIZE  = 'heading_xxlarge_font_size';
	public const HEADING_XLARGE_FONT_SIZE  	= 'heading_xlarge_font_size';
	public const HEADING_LARGE_FONT_SIZE   	= 'heading_large_font_size';
	public const HEADING_FONT_SIZE         	= 'heading_font_size';
	public const HEADING_SMALL_FONT_SIZE   	= 'heading_small_font_size';
	public const HEADING_XSMALL_FONT_SIZE  	= 'heading_xsmall_font_size';
	public const HEADING_XXSMALL_FONT_SIZE 	= 'heading_xxsmall_font_size';
	public const HEADING_XXXSMALL_FONT_SIZE = 'heading_xxxsmall_font_size';

	public const HEADING_XXLARGE_FONT_SIZE_DEFAULT  = '40px';
	public const HEADING_XLARGE_FONT_SIZE_DEFAULT  	= '36px';
	public const HEADING_LARGE_FONT_SIZE_DEFAULT   	= '32px';
	public const HEADING_FONT_SIZE_DEFAULT         	= '28px';
	public const HEADING_SMALL_FONT_SIZE_DEFAULT   	= '24px';
	public const HEADING_XSMALL_FONT_SIZE_DEFAULT  	= '20px';
	public const HEADING_XXSMALL_FONT_SIZE_DEFAULT 	= '18px';
	public const HEADING_XXXSMALL_FONT_SIZE_DEFAULT = '16px';

	public const HEADING_XXLARGE_LINE_HEIGHT  	= 'heading_xxlarge_line_height';
	public const HEADING_XLARGE_LINE_HEIGHT  	= 'heading_xlarge_line_height';
	public const HEADING_LARGE_LINE_HEIGHT   	= 'heading_large_line_height';
	public const HEADING_LINE_HEIGHT         	= 'heading_line_height';
	public const HEADING_SMALL_LINE_HEIGHT   	= 'heading_small_line_height';
	public const HEADING_XSMALL_LINE_HEIGHT  	= 'heading_xsmall_line_height';
	public const HEADING_XXSMALL_LINE_HEIGHT 	= 'heading_xxsmall_line_height';
	public const HEADING_XXXSMALL_LINE_HEIGHT 	= 'heading_xxxsmall_line_height';

	public const HEADING_XXLARGE_LINE_HEIGHT_DEFAULT  	= '1.4';
	public const HEADING_XLARGE_LINE_HEIGHT_DEFAULT  	= '1.4';
	public const HEADING_LARGE_LINE_HEIGHT_DEFAULT   	= '1.4';
	public const HEADING_LINE_HEIGHT_DEFAULT         	= '1.4';
	public const HEADING_SMALL_LINE_HEIGHT_DEFAULT   	= '1.4';
	public const HEADING_XSMALL_LINE_HEIGHT_DEFAULT  	= '1.4';
	public const HEADING_XXSMALL_LINE_HEIGHT_DEFAULT 	= '1.4';
	public const HEADING_XXXSMALL_LINE_HEIGHT_DEFAULT 	= '1.4';

	public const HEADING_XXLARGE_WEIGHT  	= 'heading_xxlarge_weight';
	public const HEADING_XLARGE_WEIGHT  	= 'heading_xlarge_weight';
	public const HEADING_LARGE_WEIGHT   	= 'heading_large_weight';
	public const HEADING_WEIGHT         	= 'heading_weight';
	public const HEADING_SMALL_WEIGHT   	= 'heading_small_weight';
	public const HEADING_XSMALL_WEIGHT  	= 'heading_xsmall_weight';
	public const HEADING_XXSMALL_WEIGHT 	= 'heading_xxsmall_weight';
	public const HEADING_XXXSMALL_WEIGHT 	= 'heading_xxxsmall_weight';

	public const HEADING_XXLARGE_WEIGHT_DEFAULT  	= '--font-weight-medium';
	public const HEADING_XLARGE_WEIGHT_DEFAULT  	= '--font-weight-medium';
	public const HEADING_LARGE_WEIGHT_DEFAULT   	= '--font-weight-medium';
	public const HEADING_WEIGHT_DEFAULT         	= '--font-weight-medium';
	public const HEADING_SMALL_WEIGHT_DEFAULT   	= '--font-weight-medium';
	public const HEADING_XSMALL_WEIGHT_DEFAULT  	= '--font-weight-medium';
	public const HEADING_XXSMALL_WEIGHT_DEFAULT 	= '--font-weight-medium';
	public const HEADING_XXXSMALL_WEIGHT_DEFAULT 	= '--font-weight-medium';

	public const HEADING_FONT_WEIGHT_CHOICES = [
		'--font-weight-regular'  => '400 / --font-weight-regular',
		'--font-weight-medium'   => '500 / --font-weight-medium',
		'--font-weight-semibold' => '600 / --font-weight-semibold',
		'--font-weight-bold'     => '700 / --font-weight-bold',
	];

	public const HEADING_FONT_SIZE_CHOICES = [
		'40px' => '40px',
		'36px' => '36px',
		'32px' => '32px',
		'28px' => '28px',
		'24px' => '24px',
		'20px' => '20px',
		'18px' => '18px',
		'16px' => '16px',
	];

	public const HEADING_FONT_LINE_HEIGHT_CHOICES = [
		'1.17' => '1.17',
		'1.2'  => '1.2',
		'1.25' => '1.25',
		'1.3'  => '1.3',
		'1.33' => '1.33',
		'1.4'  => '1.4',
	];

	public function section_title() {
		$this->wp_customize->add_section( self::FONTS_SECTION, [
			'title'    => __( 'Fonts', 'tribe' ),
			'priority' => 30,
		] );

		return $this;
	}

	public function primary_font() {
		$this->wp_customize->add_setting( self::PRIMARY_FONT, [
			'default' => Web_Fonts::POPPINS,
		] );
		$this->wp_customize->add_control( self::PRIMARY_FONT, [
				'label'       => __( 'Primary Font', 'tribe' ),
				'description' => __( 'Font to use for title, headline, & button text.', 'tribe' ),
				'section'     => self::FONTS_SECTION,
				'settings'    => self::PRIMARY_FONT,
				'type'        => 'select',
				'choices'     => $this->get_font_choices(),
				'priority'    => 10,
			]
		);

		return $this;
	}

	public function secondary_font() {
		$this->wp_customize->add_setting( self::SECONDARY_FONT, [
			'default' => Web_Fonts::POPPINS,
		] );
		$this->wp_customize->add_control( self::SECONDARY_FONT, [
				'label'       => __( 'Secondary Font', 'tribe' ),
				'description' => __( 'Font to use for body & paragraph text.', 'tribe' ),
				'section'     => self::FONTS_SECTION,
				'settings'    => self::SECONDARY_FONT,
				'type'        => 'select',
				'choices'     => $this->get_font_choices(),
				'priority'    => 20,
			]
		);

		return $this;
	}

	public function primary_font_color() {
		$this->wp_customize->add_setting( self::PRIMARY_FONT_COLOR, [ 'default' => self::PRIMARY_FONT_COLOR_DEFAULT ] );
		$this->wp_customize->add_control( new Tribe_Color_Picker(
			$this->wp_customize,
			self::PRIMARY_FONT_COLOR,
			[
				'is_source'   => true,
				'label'       => __( 'Primary Font Color', 'tribe' ),
				'description' => esc_html__( 'Applied to headings throughout the theme.', 'tribe' ),
				'section'     => self::FONTS_SECTION,
				'settings'    => self::PRIMARY_FONT_COLOR,
				'priority'    => 15,
			]
		) );

		return $this;
	}

	public function secondary_font_color() {
		$this->wp_customize->add_setting( self::SECONDARY_FONT_COLOR, [ 'default' => self::SECONDARY_FONT_COLOR_DEFAULT ] );
		$this->wp_customize->add_control( new Tribe_Color_Picker(
			$this->wp_customize,
			self::SECONDARY_FONT_COLOR,
			[
				'is_source'   => true,
				'label'       => __( 'Secondary Font Color', 'tribe' ),
				'description' => esc_html__( 'Applied to body text, captions, and labels throughout the theme.', 'tribe' ),
				'section'     => self::FONTS_SECTION,
				'settings'    => self::SECONDARY_FONT_COLOR,
				'priority'    => 25,
			]
		) );

		return $this;
	}

	public function heading_xxlarge_weight() {
		$this->wp_customize->add_setting( self::HEADING_XXLARGE_WEIGHT, [
			'default' => self::HEADING_XXLARGE_WEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XXLARGE_WEIGHT, [
				'label'    => __( 'XXLarge Heading Weight', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XXLARGE_WEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_WEIGHT_CHOICES,
				'priority' => 37,
			]
		);

		return $this;
	}

	public function heading_xlarge_weight() {
		$this->wp_customize->add_setting( self::HEADING_XLARGE_WEIGHT, [
			'default' => self::HEADING_XLARGE_WEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XLARGE_WEIGHT, [
				'label'    => __( 'XLarge Heading Weight', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XLARGE_WEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_WEIGHT_CHOICES,
				'priority' => 40,
			]
		);

		return $this;
	}

	public function heading_large_weight() {
		$this->wp_customize->add_setting( self::HEADING_LARGE_WEIGHT, [
			'default' => self::HEADING_LARGE_WEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_LARGE_WEIGHT, [
				'label'    => __( 'Large Heading Weight', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_LARGE_WEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_WEIGHT_CHOICES,
				'priority' => 45,
			]
		);

		return $this;
	}

	public function heading_weight() {
		$this->wp_customize->add_setting( self::HEADING_WEIGHT, [
			'default' => self::HEADING_WEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_WEIGHT, [
				'label'    => __( 'Heading Weight', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_WEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_WEIGHT_CHOICES,
				'priority' => 50,
			]
		);

		return $this;
	}

	public function heading_small_weight() {
		$this->wp_customize->add_setting( self::HEADING_SMALL_WEIGHT, [
			'default' => self::HEADING_SMALL_WEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_SMALL_WEIGHT, [
				'label'    => __( 'Small Heading Weight', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_SMALL_WEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_WEIGHT_CHOICES,
				'priority' => 55,
			]
		);

		return $this;
	}

	public function heading_xsmall_weight() {
		$this->wp_customize->add_setting( self::HEADING_XSMALL_WEIGHT, [
			'default' => self::HEADING_XSMALL_WEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XSMALL_WEIGHT, [
				'label'    => __( 'XSmall Heading Weight', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XSMALL_WEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_WEIGHT_CHOICES,
				'priority' => 60,
			]
		);

		return $this;
	}

	public function heading_xxsmall_weight() {
		$this->wp_customize->add_setting( self::HEADING_XXSMALL_WEIGHT, [
			'default' => self::HEADING_XXSMALL_WEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XXSMALL_WEIGHT, [
				'label'    => __( 'XXSmall Heading Weight', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XXSMALL_WEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_WEIGHT_CHOICES,
				'priority' => 65,
			]
		);

		return $this;
	}

	public function heading_xxxsmall_weight() {
		$this->wp_customize->add_setting( self::HEADING_XXXSMALL_WEIGHT, [
			'default' => self::HEADING_XXXSMALL_WEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XXXSMALL_WEIGHT, [
				'label'    => __( 'XXXSmall Heading Weight', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XXXSMALL_WEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_WEIGHT_CHOICES,
				'priority' => 70,
			]
		);

		return $this;
	}

	public function heading_xxlarge_font_size() {
		$this->wp_customize->add_setting( self::HEADING_XXLARGE_FONT_SIZE, [
			'default' => self::HEADING_XXLARGE_FONT_SIZE_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XXLARGE_FONT_SIZE, [
				'label'    => __( 'XXLarge Heading Font-Size', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XXLARGE_FONT_SIZE,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_SIZE_CHOICES,
				'priority' => 38,
			]
		);

		return $this;
	}

	public function heading_xlarge_font_size() {
		$this->wp_customize->add_setting( self::HEADING_XLARGE_FONT_SIZE, [
			'default' => self::HEADING_XLARGE_FONT_SIZE_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XLARGE_FONT_SIZE, [
				'label'    => __( 'XLarge Heading Font-Size', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XLARGE_FONT_SIZE,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_SIZE_CHOICES,
				'priority' => 41,
			]
		);

		return $this;
	}

	public function heading_large_font_size() {
		$this->wp_customize->add_setting( self::HEADING_LARGE_FONT_SIZE, [
			'default' => self::HEADING_LARGE_FONT_SIZE_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_LARGE_FONT_SIZE, [
				'label'    => __( 'Large Heading Font-Size', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_LARGE_FONT_SIZE,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_SIZE_CHOICES,
				'priority' => 46,
			]
		);

		return $this;
	}

	public function heading_font_size() {
		$this->wp_customize->add_setting( self::HEADING_FONT_SIZE, [
			'default' => self::HEADING_FONT_SIZE_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_FONT_SIZE, [
				'label'    => __( 'Heading Font-Size', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_FONT_SIZE,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_SIZE_CHOICES,
				'priority' => 51,
			]
		);

		return $this;
	}

	public function heading_small_font_size() {
		$this->wp_customize->add_setting( self::HEADING_SMALL_FONT_SIZE, [
			'default' => self::HEADING_SMALL_FONT_SIZE_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_SMALL_FONT_SIZE, [
				'label'    => __( 'Small Heading Font-Size', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_SMALL_FONT_SIZE,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_SIZE_CHOICES,
				'priority' => 56,
			]
		);

		return $this;
	}

	public function heading_xsmall_font_size() {
		$this->wp_customize->add_setting( self::HEADING_XSMALL_FONT_SIZE, [
			'default' => self::HEADING_XSMALL_FONT_SIZE_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XSMALL_FONT_SIZE, [
				'label'    => __( 'XSmall Heading Font-Size', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XSMALL_FONT_SIZE,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_SIZE_CHOICES,
				'priority' => 61,
			]
		);

		return $this;
	}

	public function heading_xxsmall_font_size() {
		$this->wp_customize->add_setting( self::HEADING_XXSMALL_FONT_SIZE, [
			'default' => self::HEADING_XXSMALL_FONT_SIZE_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XXSMALL_FONT_SIZE, [
				'label'    => __( 'XXSmall Heading Font-Size', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XXSMALL_FONT_SIZE,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_SIZE_CHOICES,
				'priority' => 65,
			]
		);

		return $this;
	}

	public function heading_xxxsmall_font_size() {
		$this->wp_customize->add_setting( self::HEADING_XXXSMALL_FONT_SIZE, [
			'default' => self::HEADING_XXXSMALL_FONT_SIZE_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XXXSMALL_FONT_SIZE, [
				'label'    => __( 'XXXSmall Heading Font-Size', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XXXSMALL_FONT_SIZE,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_SIZE_CHOICES,
				'priority' => 70,
			]
		);

		return $this;
	}

	public function heading_xxlarge_line_height() {
		$this->wp_customize->add_setting( self::HEADING_XXLARGE_LINE_HEIGHT, [
			'default' => self::HEADING_XXLARGE_LINE_HEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XXLARGE_LINE_HEIGHT, [
				'label'    => __( 'XXLarge Heading Line Height', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XXLARGE_LINE_HEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_LINE_HEIGHT_CHOICES,
				'priority' => 39,
			]
		);

		return $this;
	}

	public function heading_xlarge_line_height() {
		$this->wp_customize->add_setting( self::HEADING_XLARGE_LINE_HEIGHT, [
			'default' => self::HEADING_XLARGE_LINE_HEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XLARGE_LINE_HEIGHT, [
				'label'    => __( 'XLarge Heading Line Height', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XLARGE_LINE_HEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_LINE_HEIGHT_CHOICES,
				'priority' => 42,
			]
		);

		return $this;
	}

	public function heading_large_line_height() {
		$this->wp_customize->add_setting( self::HEADING_LARGE_LINE_HEIGHT, [
			'default' => self::HEADING_LARGE_LINE_HEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_LARGE_LINE_HEIGHT, [
				'label'    => __( 'Large Heading Line Height', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_LARGE_LINE_HEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_LINE_HEIGHT_CHOICES,
				'priority' => 47,
			]
		);

		return $this;
	}

	public function heading_line_height() {
		$this->wp_customize->add_setting( self::HEADING_LINE_HEIGHT, [
			'default' => self::HEADING_LINE_HEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_LINE_HEIGHT, [
				'label'    => __( 'Heading Line Height', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_LINE_HEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_LINE_HEIGHT_CHOICES,
				'priority' => 52,
			]
		);

		return $this;
	}

	public function heading_small_line_height() {
		$this->wp_customize->add_setting( self::HEADING_SMALL_LINE_HEIGHT, [
			'default' => self::HEADING_SMALL_LINE_HEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_SMALL_LINE_HEIGHT, [
				'label'    => __( 'Small Heading Line Height', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_SMALL_LINE_HEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_LINE_HEIGHT_CHOICES,
				'priority' => 57,
			]
		);

		return $this;
	}

	public function heading_xsmall_line_height() {
		$this->wp_customize->add_setting( self::HEADING_XSMALL_LINE_HEIGHT, [
			'default' => self::HEADING_XSMALL_LINE_HEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XSMALL_LINE_HEIGHT, [
				'label'    => __( 'XSmall Heading Line Height', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XSMALL_LINE_HEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_LINE_HEIGHT_CHOICES,
				'priority' => 62,
			]
		);

		return $this;
	}

	public function heading_xxsmall_line_height() {
		$this->wp_customize->add_setting( self::HEADING_XXSMALL_LINE_HEIGHT, [
			'default' => self::HEADING_XXSMALL_LINE_HEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XXSMALL_LINE_HEIGHT, [
				'label'    => __( 'XXSmall Heading Line Height', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XXSMALL_LINE_HEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_LINE_HEIGHT_CHOICES,
				'priority' => 67,
			]
		);

		return $this;
	}

	public function heading_xxxsmall_line_height() {
		$this->wp_customize->add_setting( self::HEADING_XXXSMALL_LINE_HEIGHT, [
			'default' => self::HEADING_XXXSMALL_LINE_HEIGHT_DEFAULT,
		] );
		$this->wp_customize->add_control( self::HEADING_XXXSMALL_LINE_HEIGHT, [
				'label'    => __( 'XXXSmall Heading Line Height', 'tribe' ),
				'section'  => self::FONTS_SECTION,
				'settings' => self::HEADING_XXXSMALL_LINE_HEIGHT,
				'type'     => 'select',
				'choices'  => self::HEADING_FONT_LINE_HEIGHT_CHOICES,
				'priority' => 72,
			]
		);

		return $this;
	}



	protected function get_font_choices() {
		return wp_list_pluck( Web_Fonts::get_google_fonts(), 'label' );
	}
}
