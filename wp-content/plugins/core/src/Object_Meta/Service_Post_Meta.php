<?php
declare( strict_types=1 );

namespace Tribe\Project\Object_Meta;

use Tribe\Libs\ACF\ACF_Meta_Group;
use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Field_Group;
use Tribe\Libs\ACF\Group;
use Tribe\Libs\ACF\Repeater;
use Tribe\Project\Post_Types\Service_Post\Service_Post;
use Tribe\Project\Template_Tags\Template_Tags;
use Tribe\Project\Taxonomies\Ssl_Tool\Ssl_Tool;
use Tribe\Project\Templates\Components\blocks\table_of_contents\Table_of_Contents_Block_Controller;
use Tribe\Project\Templates\Components\Traits\Handles_MemberPress_Permissions;

class Service_Post_Meta extends ACF_Meta_Group {
	use Handles_MemberPress_Permissions;

	public const NAME                           = 'service_meta';
	public const SUBTITLE                       = 'subtitle';
	public const REVIEWED_ON                    = 'reviewed_on';
	public const REVIEWER_AS_COAUTHOR           = 'reviewer_as_coauthor';
	public const REVIEWER_CONTRIBUTOR           = 'reviewer_contributor';
	public const ENTITY_TYPE                    = 'entity_type';
	public const ENTITY_TYPE_REVIEWER           = 'reviewer';
	public const ENTITY_TYPE_CONTRIBUTOR        = 'contributor';
	public const USERS                          = 'users';
	public const MULTIAUTHOR                    = 'multiauthor';
	public const SHOW_AUTHOR_BADGE              = 'show_author_badge';
	public const AUTHOR_BADGE                   = 'author_badge';
	public const AUTHOR_BADGE_TOOL_EXPERT       = 'tool_expert';
	public const INTRODUCTION                   = 'introduction';
	public const HIDE_QUICK_SUMMARY             = 'hide_quick_summary';
	public const HIDE_AUTHOR                    = 'hide_author';
	public const HIDE_ADS                       = 'hide_ads';
	public const HIDE_CONTENT_ADS               = 'hide_content_ads';
	public const HIDE_DATE                      = 'hide_date';
	public const HIDE_SECTION_JUMP_LINKS        = 'hide_section_jump_links';
	public const SECTION_JUMP_LINKS             = 'section_jump_links';
	public const BLOCK_ID                       = 'block_id';
	public const LABEL                          = 'label';
	public const REPLACE_MAIN_AD_FOR_THREE_PACK = 'replace_main_ad_for_three_pack';
	public const SECTION_JUMP_LINKS_BUTTON      = 'section_jump_links_button';
	public const LISTICLE_MAX_SIZE              = 'listicle_max_size';
	public const LISTICLE_CATEGORY              = 'listicle_category';
	public const TITLE_TEMPLATE                 = 'title_template';
	public const ALTERNATIVE_TO_SERVICE         = 'alternative_to_service';

	public function get_keys(): array {
		return [
			static::HIDE_AUTHOR,
			static::HIDE_ADS,
			static::HIDE_CONTENT_ADS,
			static::HIDE_DATE,
			static::HIDE_SECTION_JUMP_LINKS,
			static::SECTION_JUMP_LINKS,
			static::REVIEWED_ON,
			static::REVIEWER_AS_COAUTHOR,
			static::MULTIAUTHOR,
			static::SHOW_AUTHOR_BADGE,
			static::AUTHOR_BADGE,
			static::AUTHOR_BADGE_TOOL_EXPERT,
			static::LISTICLE_MAX_SIZE,
			static::LISTICLE_CATEGORY,
			static::TITLE_TEMPLATE,
			static::ALTERNATIVE_TO_SERVICE,
			static::HIDE_QUICK_SUMMARY,
			static::SHOW_REGWALL,
		];
	}

	public function get_value( $key, $post_id = 'option' ) {
		return parent::get_value( $post_id, $key );
	}

	public function get_group_config(): array {
		$group = new Group( self::NAME, $this->object_types );
		$group->set( 'title', __( 'Service Options', 'tribe' ) );
		$group->set( 'location', [
			[
				[
					'param'    => 'post_type',
					'operator' => '==',
					'value'    => Service_Post::NAME,
				],
			],
		] );

		$group->add_field( $this->get_title_template_field() );
		$group->add_field( $this->get_introduction_field() );
		$group->add_field( $this->get_subtitle_field() );
		$group->add_field( $this->get_alternative_to_service_field() );
		$group->add_field( $this->get_reviewed_on_field() );
		$group->add_field( $this->get_reviewer_as_coauthor_field() );
		$group->add_field( $this->get_reviewer_contributor_field() );
		$group->add_field( $this->get_multiauthor_field() );
		$group->add_field( $this->get_hide_quick_summary_field() );
		$group->add_field( $this->get_show_author_badge_field() );
		$group->add_field( $this->get_author_badge_field() );
		$group->add_field( $this->get_hide_section_jump_links_field() );
		$group->add_field( $this->get_section_jump_links_button_field() );
		$group->add_field( $this->get_section_jump_links_field() );
		$group->add_field( $this->get_hide_ads_field() );
		$group->add_field( $this->get_hide_author_field() );
		$group->add_field( $this->get_hide_date_field() );
		$group->add_field( $this->get_replace_main_ad_for_three_pack_field() );
		$group->add_field( $this->get_listicle_max_size_field() );
		$group->add_field( $this->get_listicle_category_field() );

		if ( $this->is_regwall_active() ) {
			$group->add_field( $this->get_show_regwall_field() );
		}

		return $group->get_attributes();
	}

	private function get_subtitle_field(): Field {
		$field = new Field( self::NAME . '_' . self::SUBTITLE );
		$field->set_attributes( [
			'label'        => __( 'Subtitle', 'tribe' ),
			'instructions' => __( 'Appears below the Service Post title', 'tribe' ),
			'name'         => self::SUBTITLE,
			'type'         => 'text',
		] );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_title_template_field(): Field {
		$field = new Field( self::NAME . '_' . self::TITLE_TEMPLATE );
		$field->set_attributes( [
			'label'        => __( 'Title Template', 'tribe' ),
			'instructions' => 'Keep it empty to not automate the title update.<br><b style="color:red;">This will NOT update the slug.</b><br><br>Available Tags:<br><code>' . Template_Tags::TEMPLATE_TAG_SSL_CATEGORY . '</code> to add the name of the SSL Category<br><code>' . Template_Tags::TEMPLATE_TAG_LISTICLE_TOTAL . '</code> to add the total number of items in the listicle (the number you see in the overflow).<br><code>' . Template_Tags::TEMPLATE_TAG_YEAR . '</code> to add the current year number ' . date( "Y" ) . '<br><code>' . Template_Tags::TEMPLATE_TAG_NEXT_YEAR . '</code> to add the next year number ' . date( 'Y', strtotime( '+1 year' ) ) . '<br><small>PS.: The title is updated when the post is saved/updated AND one time a day. After saving the post you need to refresh the page to see the new title.</small>',
			'name'         => self::TITLE_TEMPLATE,
			'type'         => 'text',
		] );

		return $field;
	}

	private function get_introduction_field(): Field {
		$field = new Field( self::NAME . '_' . self::INTRODUCTION );
		$field->set_attributes( [
			'label'        => __( 'Introduction', 'tribe' ),
			'name'         => self::INTRODUCTION,
			'instructions' => 'Available Tags:<br><code>' . Template_Tags::TEMPLATE_TAG_SSL_CATEGORY . '</code> to add the name of the SSL Category<br><code>' . Template_Tags::TEMPLATE_TAG_LISTICLE_TOTAL . '</code> to add the total number of items in the listicle (the number you see in the overflow).<br><code>' . Template_Tags::TEMPLATE_TAG_YEAR . '</code> to add the current year number ' . date( "Y" ) . '<br><code>' . Template_Tags::TEMPLATE_TAG_NEXT_YEAR . '</code> to add the next year number ' . date( 'Y', strtotime( '+1 year' ) ),
			'type'         => 'wysiwyg',
			'media_upload' => 0,
			'toolbar'      => 'basic',
		] );

		return $field;
	}

	private function get_reviewed_on_field(): Field {
		$field = new Field( self::NAME . '_' . self::REVIEWED_ON );
		$field->set_attributes( [
			'label'          => __( 'Reviewed on', 'tribe' ),
			'name'           => self::REVIEWED_ON,
			'type'           => 'date_picker',
			'display_format' => 'F j, Y',
			'return_format'  => 'Y-m-d',
			'first_day'      => 1,
			'allow_null'     => true,
			'wrapper'        => [
				'width' => '50%',
			],
		] );

		return $field;
	}

	private function get_reviewer_as_coauthor_field(): Field {
		$field = new Field( self::NAME . '_' . self::REVIEWER_AS_COAUTHOR );
		$field->set_attributes( [
			'label'         => __( 'Add Reviewer as co-author?', 'tribe' ),
			'name'          => self::REVIEWER_AS_COAUTHOR,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
			'wrapper'       => [
				'width' => '50%',
			],
		] );

		return $field;
	}

	private function get_reviewer_contributor_field(): Repeater {
		$repeater = new Repeater( self::NAME . '_' . self::REVIEWER_CONTRIBUTOR );
		$repeater->set_attributes( [
			'label'        => __( 'Add Reviewer/Contributor', 'tribe' ),
			'name'         => self::REVIEWER_CONTRIBUTOR,
			'layout'       => 'block',
			'min'          => 0,
			'max'          => 2,
			'button_label' => __( 'Add new entity', 'tribe' ),
		] );

		$repeater->add_field( new Field( self::ENTITY_TYPE, [
			'label'         => __( 'Type', 'tribe' ),
			'name'          => self::ENTITY_TYPE,
			'type'          => 'select',
			'choices'       => [
				self::ENTITY_TYPE_REVIEWER    => __( 'Reviewer', 'tribe' ),
				self::ENTITY_TYPE_CONTRIBUTOR => __( 'Contributor', 'tribe' ),
			],
			'default_value' => self::ENTITY_TYPE_REVIEWER,
			'multiple'      => 0,
			'ui'            => 1,
			'allow_null'    => 0,
		] ) );

		$repeater->add_field( new Field( self::USERS, [
			'label'        => __( 'Users', 'tribe' ),
			'name'         => self::USERS,
			'type'         => 'user',
			'allow_null'   => false,
			'multiple'     => true,
			'required'     => true,
			'role'         => [
				0 => 'author',
				1 => 'editor',
				2 => 'administrator',
				4 => 'content_manager',
				5 => 'sales_content_manager',
				6 => 'contributor',
			],
			'instructions' => __( 'You can add multiple reviewers/contributors in this field.', 'tribe' ),
		] ) );

		return $repeater;
	}

	private function get_multiauthor_field(): Field {
		$field = new Field( self::NAME . '_' . self::MULTIAUTHOR );
		$field->set_attributes( [
			'label'         => __( 'Multiauthor selector', 'tribe' ),
			'name'          => self::MULTIAUTHOR,
			'type'          => 'user',
			'allow_null'    => true,
			'multiple'      => true,
			'return_format' => 'id',
			'role'          => [
				0 => 'author',
				1 => 'editor',
				2 => 'administrator',
				4 => 'content_manager',
				5 => 'sales_content_manager',
				6 => 'contributor',
			],
		] );

		return $field;
	}

	/**
	 * Services should have the Hide Quick Summary option.
	 *
	 * @return Field
	 */
	private function get_hide_quick_summary_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_QUICK_SUMMARY );
		$field->set_attributes( [
			'label'         => __( 'Hide Quick Summary / Introduction?', 'tribe' ),
			'name'          => self::HIDE_QUICK_SUMMARY,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	private function get_show_author_badge_field(): Field {
		$field = new Field( self::NAME . '_' . self::SHOW_AUTHOR_BADGE );
		$field->set_attributes( [
			'label'         => __( 'Show Author Badge?', 'tribe' ),
			'name'          => self::SHOW_AUTHOR_BADGE,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
			'disabled'      => 1,
		] );

		return $field;
	}

	private function get_author_badge_field(): Field {
		$field = new Field( self::NAME . '_' . self::AUTHOR_BADGE );
		$field->set_attributes( [
			'label'             => __( 'Author Badge', 'tribe' ),
			'name'              => self::AUTHOR_BADGE,
			'type'              => 'select',
			'default_value'     => self::AUTHOR_BADGE_TOOL_EXPERT,
			'ui'                => 1,
			'choices'           => [
				self::AUTHOR_BADGE_TOOL_EXPERT => __( 'Tool Expert', 'tribe' ),
			],
			'conditional_logic' => [
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::SHOW_AUTHOR_BADGE,
						'operator' => '==',
						'value'    => '1',
					],
				],
			],
			'disabled'          => 1,
		] );

		return $field;
	}

	private function get_hide_author_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_AUTHOR );
		$field->set_attributes( [
			'label'         => __( 'Hide Author?', 'tribe' ),
			'name'          => self::HIDE_AUTHOR,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	private function get_hide_ads_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_ADS );
		$field->set_attributes( [
			'label'         => __( 'Hide Ads', 'tribe' ),
			'name'          => self::HIDE_ADS,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	private function get_hide_date_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_DATE );
		$field->set_attributes( [
			'label'         => __( 'Hide Published Date?', 'tribe' ),
			'name'          => self::HIDE_DATE,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	private function get_hide_section_jump_links_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_SECTION_JUMP_LINKS );
		$field->set_attributes( [
			'label'         => __( 'Hide Table Of Contents?', 'tribe' ),
			'name'          => self::HIDE_SECTION_JUMP_LINKS,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	private function get_section_jump_links_field(): Repeater {
		$repeater = new Repeater( self::NAME . '_' . self::SECTION_JUMP_LINKS );
		$repeater->set_attributes( [
			'label'        => __( 'Table Of Contents links', 'tribe' ),
			'instructions' => 'In a <b>Perfect Listicle</b> you can use the tag <code>' . Table_of_Contents_Block_Controller::TOC_TEMPLATE_TAG . '</code> to display the list of services in a ToC position.',
			'name'         => self::SECTION_JUMP_LINKS,
			'layout'       => 'block',
			'min'          => 0,
			'button_label' => __( 'Add new section', 'tribe' ),
		] );

		$repeater->add_field( new Field( self::BLOCK_ID, [
			'label' => __( 'Block ID', 'tribe' ),
			'name'  => self::BLOCK_ID,
			'type'  => 'text',
		] ) );

		$repeater->add_field( new Field( self::LABEL, [
			'label' => __( 'Label', 'tribe' ),
			'name'  => self::LABEL,
			'type'  => 'text',
		] ) );

		return $repeater;
	}

	private function get_section_jump_links_button_field(): Field {
		$field = new Field( self::NAME . '_' . self::SECTION_JUMP_LINKS_BUTTON );
		$field->set_attributes( [
			'label'   => __( 'Table Of Contents - Options', 'tribe' ),
			'name'    => self::SECTION_JUMP_LINKS_BUTTON,
			'type'    => 'message',
			'message' => '<button class="button button-primary" id="acf-section-jump-links-option-populate-h2">Populate Table Of Contents (H2)</button> <button class="button button-primary" id="acf-section-jump-links-option-populate-h3">Populate Table Of Contents (H2+H3)</button> <button class="button" id="acf-section-jump-links-option-rewrite">Convert Labels to Sentence Case (Uses AI)</button> <button class="button" id="acf-section-jump-links-option-clear">Clear Table Of Contents</button> <span id="acf-section-jump-links-option-anchor"></span>',
		] );

		return $field;
	}

	/**
	 * The main header will be replaced by a three pack ad
	 *
	 * @return Field
	 */
	private function get_replace_main_ad_for_three_pack_field(): Field {
		$field = new Field( self::NAME . '_' . self::REPLACE_MAIN_AD_FOR_THREE_PACK );
		$field->set_attributes( [
			'label'         => __( 'Replace Main Ad for 3-Pack Ad?', 'tribe' ),
			'name'          => self::REPLACE_MAIN_AD_FOR_THREE_PACK,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_listicle_max_size_field(): Field {
		$field = new Field( self::NAME . '_' . self::LISTICLE_MAX_SIZE );
		$field->set_attributes( [
			'label'         => __( 'Listicle Max Size', 'tribe' ),
			'instructions'  => __( 'This is based in the size of the overflow and this will be automatically updated when the post is saved.<br>(Refresh after updating the post.)', 'tribe' ),
			'name'          => self::LISTICLE_MAX_SIZE,
			'type'          => 'number',
			'default_value' => 0,
		] );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_listicle_category_field(): Field {
		$field = new Field( self::NAME . '_' . self::LISTICLE_CATEGORY );
		$field->set_attributes( [
			'label'         => __( 'SSL Listicle (WPP ID)', 'tribe' ),
			'instructions'  => __( 'This is based in the SSL Listicle selected in the first Shortlist in the content and this will be automatically updated when the post is saved.<br>(Refresh after updating the post.)', 'tribe' ),
			'name'          => self::LISTICLE_CATEGORY,
			'type'          => 'number',
			'default_value' => 0,
		] );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_alternative_to_service_field(): Field {
		$field = new Field( self::NAME . '_' . self::ALTERNATIVE_TO_SERVICE );
		$field->set_attributes( [
			'label'         => __( 'Alternative to Service', 'tribe' ),
			'name'          => self::ALTERNATIVE_TO_SERVICE,
			'type'          => 'taxonomy',
			'field_type'    => 'select',
			'instructions'  => __( 'Choose a primary service for which the alternatives are considered. Required to be filled when use Automated Feature Image for Alternatives.', 'tribe' ),
			'taxonomy'      => Ssl_Tool::NAME,
			'return_format' => 'id',
			'disabled'      => 1,
		] );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_show_regwall_field(): Field {
		$field = new Field( self::NAME . '_' . self::SHOW_REGWALL );
		$field->set_attributes( [
			'label'         => __( 'Enable Regwall', 'tribe' ),
			'instructions'  => __( 'This will hide the content for non-members after 3 paragraph blocks. The MemberPress rule needs to be setted up in the settings page.', 'tribe' ),
			'name'          => self::SHOW_REGWALL,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}
}
