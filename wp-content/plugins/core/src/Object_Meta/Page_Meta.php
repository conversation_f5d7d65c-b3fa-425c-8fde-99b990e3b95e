<?php
declare( strict_types=1 );

namespace Tribe\Project\Object_Meta;

use Tribe\Libs\ACF\ACF_Meta_Group;
use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Group;
use Tribe\Libs\ACF\Repeater;
use Tribe\Project\Integrations\Memberpress\Memberpress;
use Tribe\Project\Post_Types\Page\Page;
use Tribe\Project\Templates\Components\Traits\Handles_MemberPress_Permissions;

class Page_Meta extends ACF_Meta_Group {
	use Handles_MemberPress_Permissions;

	public const NAME = 'page_meta';

	public const HIDE_FOOTER         	   = 'hide_footer';
	public const HIDE_FORM_FOOTER    = 'hide_form_footer';
	public const SHOW_ADS            	   = 'show_ads';
	public const SHOW_MEMBERSHIP_NAV 	   = 'show_membership_nav';
	public const HIDE_SECTION_JUMP_LINKS   = 'hide_section_jump_links';
	public const SECTION_JUMP_LINKS        = 'section_jump_links';
	public const BLOCK_ID                  = 'block_id';
	public const LABEL                     = 'label';
	public const SECTION_JUMP_LINKS_BUTTON = 'section_jump_links_button';

	public function get_keys() {
		return [
			static::SHOW_ADS,
			static::HIDE_FOOTER,
			static::HIDE_FORM_FOOTER,
			static::SHOW_MEMBERSHIP_NAV,
			static::SHOW_REGWALL,
			static::HIDE_SECTION_JUMP_LINKS,
			static::SECTION_JUMP_LINKS,
			static::SECTION_JUMP_LINKS_BUTTON,
		];
	}

	public function get_value( $key, $post_id = 'option' ) {
		return parent::get_value( $post_id, $key );
	}

	public function get_group_config(): array {
		$group = new Group( self::NAME, $this->object_types );
		$group->set( 'title', __( 'Page Options', 'tribe' ) );
		$group->set( 'location', [
			[
				[
					'param'    => 'post_type',
					'operator' => '==',
					'value'    => Page::NAME,
				],
			],
		] );
		$group->add_field( $this->get_show_ads_field() );
		$group->add_field( $this->get_hide_footer_field() );
		$group->add_field( $this->get_hide_form_footer_field() );
		$group->add_field( $this->get_hide_section_jump_links_field() );
		$group->add_field( $this->get_section_jump_links_button_field() );
		$group->add_field( $this->get_section_jump_links_field() );

		if ( Memberpress::is_active() ) {
			$group->add_field( $this->get_show_membership_nav_field() );
		}

		if ( $this->is_regwall_active() ) {
			$group->add_field( $this->get_show_regwall_field() );
		}

		return $group->get_attributes();
	}

	/**
	 * Pages should have the Show Ads option.
	 *
	 * @return Field
	 */
	private function get_show_ads_field(): Field {
		$field = new Field( self::NAME . '_' . self::SHOW_ADS );
		$field->set_attributes( [
			'label'         => __( 'Show Ads?', 'tribe' ),
			'name'          => self::SHOW_ADS,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
			'instructions'  => __( 'This setting shows ads on the page.', 'tribe' ),
		] );

		return $field;
	}

	/**
	 * Pages are the only post type that can hide the footer.
	 *
	 * @return Field
	 */
	private function get_hide_footer_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_FOOTER );
		$field->set_attributes( [
			'label'         => __( 'Hide Footer?', 'tribe' ),
			'name'          => self::HIDE_FOOTER,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	private function get_hide_form_footer_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_FORM_FOOTER );
		$field->set_attributes( [
			'label'         => __( 'Hide Newsletter form from Footer?', 'tribe' ),
			'name'          => self::HIDE_FORM_FOOTER,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	private function get_show_membership_nav_field(): Field {
		$field = new Field( self::NAME . '_' . self::SHOW_MEMBERSHIP_NAV );
		$field->set_attributes( [
			'label'         => __( 'Show Membership Navigation?', 'tribe' ),
			'name'          => self::SHOW_MEMBERSHIP_NAV,
			'type'          => 'true_false',
			'default_value' => 1,
			'ui'            => 1,
		] );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_show_regwall_field(): Field {
		$field = new Field( self::NAME . '_' . self::SHOW_REGWALL );
		$field->set_attributes( [
			'label'         => __( 'Enable Regwall', 'tribe' ),
			'instructions'  => __( 'This will hide the content for non-members after 3 paragraph blocks. The MemberPress rule needs to be setted up in the settings page.', 'tribe' ),
			'name'          => self::SHOW_REGWALL,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	private function get_hide_section_jump_links_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_SECTION_JUMP_LINKS );
		$field->set_attributes( [
			'label'         => __( 'Hide Table Of Contents?', 'tribe' ),
			'name'          => self::HIDE_SECTION_JUMP_LINKS,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	private function get_section_jump_links_field(): Repeater {
		$repeater = new Repeater( self::NAME . '_' . self::SECTION_JUMP_LINKS );
		$repeater->set_attributes( [
			'label'        => __( 'Table Of Contents links', 'tribe' ),
			'name'         => self::SECTION_JUMP_LINKS,
			'layout'       => 'block',
			'min'          => 0,
			'button_label' => __( 'Add new section', 'tribe' ),
		] );

		$repeater->add_field( new Field( self::BLOCK_ID, [
			'label' => __( 'Block ID', 'tribe' ),
			'name'  => self::BLOCK_ID,
			'type'  => 'text',
		] ) );

		$repeater->add_field( new Field( self::LABEL, [
			'label' => __( 'Label', 'tribe' ),
			'name'  => self::LABEL,
			'type'  => 'text',
		] ) );

		return $repeater;
	}

	private function get_section_jump_links_button_field(): Field {
		$field = new Field( self::NAME . '_' . self::SECTION_JUMP_LINKS_BUTTON );
		$field->set_attributes( [
			'label'   => __( 'Table Of Contents - Options', 'tribe' ),
			'name'    => self::SECTION_JUMP_LINKS_BUTTON,
			'type'    => 'message',
			'message' => '<button class="button button-primary" id="acf-section-jump-links-option-populate-h2">Populate Table Of Contents (H2)</button> <button class="button button-primary" id="acf-section-jump-links-option-populate-h3">Populate Table Of Contents (H2+H3)</button> <button class="button" id="acf-section-jump-links-option-rewrite">Convert Labels to Sentence Case (Uses AI)</button> <button class="button" id="acf-section-jump-links-option-clear">Clear Table Of Contents</button> <span id="acf-section-jump-links-option-anchor"></span>',
		] );

		return $field;
	}
}
