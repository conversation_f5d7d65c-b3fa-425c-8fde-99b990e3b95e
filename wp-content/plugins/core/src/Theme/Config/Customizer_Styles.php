<?php
declare( strict_types=1 );
namespace Tribe\Project\Theme\Config;

use Tribe\Project\Theme_Customizer\Customizer_Sections\Fonts;
use Tribe\Project\Theme_Customizer\Customizer_Sections\Colors;
use Tribe\Project\Theme_Customizer\Customizer_Sections\Header;
use Tribe\Project\Theme_Customizer\Customizer_Sections\Site_Identity;

class Customizer_Styles {
	/**
	 * Renders a style block before the end of the document `<head>` to override the theme styles
	 * with options picked via the customizer UI.
	 *
	 */
	public function render_style_block(): void {
		$root_styles = '';
		$root_styles .= sprintf( "\t--global-color-primary: %s;\n", get_theme_mod( Colors::PRIMARY_COLOR, Colors::PRIMARY_COLOR_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-color-primary-hover: %s;\n", get_theme_mod( Colors::PRIMARY_FOCUS_COLOR, Colors::PRIMARY_FOCUS_COLOR_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-color-secondary: %s;\n", get_theme_mod( Colors::SECONDARY_COLOR, Colors::SECONDARY_COLOR_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-color-secondary-hover: %s;\n", get_theme_mod( Colors::SECONDARY_FOCUS_COLOR, Colors::SECONDARY_FOCUS_COLOR_DEFAULT ) );

		$root_styles .= sprintf( "\t--global-color-primary-in-dark-bg: %s;\n", get_theme_mod( Colors::PRIMARY_COLOR_IN_DARK_BG, Colors::PRIMARY_COLOR_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-color-primary-in-dark-bg-hover: %s;\n", get_theme_mod( Colors::PRIMARY_FOCUS_COLOR_IN_DARK_BG, Colors::PRIMARY_FOCUS_COLOR_DEFAULT ) );

		$root_styles .= sprintf( "\t--global-button-primary-font-color: %s;\n", get_theme_mod( Colors::BUTTON_PRIMARY_FONT_COLOR, Colors::WHITE_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-button-primary-font-color-hover: %s;\n", get_theme_mod( Colors::BUTTON_PRIMARY_FONT_FOCUS_COLOR, Colors::WHITE_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-button-primary-font-color-in-dark-bg: %s;\n", get_theme_mod( Colors::BUTTON_PRIMARY_FONT_COLOR_IN_DARK_BG, Colors::WHITE_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-button-primary-font-color-in-dark-bg-hover: %s;\n", get_theme_mod( Colors::BUTTON_PRIMARY_FONT_FOCUS_COLOR_IN_DARK_BG, Colors::WHITE_DEFAULT ) );

		$root_styles .= sprintf( "\t--global-color-alert-positive: %s;\n", get_theme_mod( Colors::POSITIVE_ALERT_COLOR, Colors::POSITIVE_ALERT_COLOR_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-color-alert-negative: %s;\n", get_theme_mod( Colors::NEGATIVE_ALERT_COLOR, Colors::NEGATIVE_ALERT_COLOR_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-color-alert-neutral: %s;\n", get_theme_mod( Colors::NEUTRAL_ALERT_COLOR, Colors::NEUTRAL_ALERT_COLOR_DEFAULT ) );

		$root_styles .= sprintf( "\t--global-font-color-primary: %s;\n", get_theme_mod( Fonts::PRIMARY_FONT_COLOR, Fonts::PRIMARY_FONT_COLOR_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-color-secondary: %s;\n", get_theme_mod( Fonts::SECONDARY_FONT_COLOR, Fonts::SECONDARY_FONT_COLOR_DEFAULT ) );

		$root_styles .= sprintf( "\t--global-font-weight-heading-xxlarge: var(%s);\n", get_theme_mod( Fonts::HEADING_XXLARGE_WEIGHT, Fonts::HEADING_XXLARGE_WEIGHT_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-weight-heading-xlarge: var(%s);\n", get_theme_mod( Fonts::HEADING_XLARGE_WEIGHT, Fonts::HEADING_XLARGE_WEIGHT_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-weight-heading-large: var(%s);\n", get_theme_mod( Fonts::HEADING_LARGE_WEIGHT, Fonts::HEADING_LARGE_WEIGHT_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-weight-heading: var(%s);\n", get_theme_mod( Fonts::HEADING_WEIGHT, Fonts::HEADING_WEIGHT_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-weight-heading-small: var(%s);\n", get_theme_mod( Fonts::HEADING_SMALL_WEIGHT, Fonts::HEADING_SMALL_WEIGHT_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-weight-heading-xsmall: var(%s);\n", get_theme_mod( Fonts::HEADING_XSMALL_WEIGHT, Fonts::HEADING_XSMALL_WEIGHT_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-weight-heading-xxsmall: var(%s);\n", get_theme_mod( Fonts::HEADING_XXSMALL_WEIGHT, Fonts::HEADING_XXSMALL_WEIGHT_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-weight-heading-xxxsmall: var(%s);\n", get_theme_mod( Fonts::HEADING_XXXSMALL_WEIGHT, Fonts::HEADING_XXXSMALL_WEIGHT_DEFAULT ) );

		$root_styles .= sprintf( "\t--global-font-size-heading-xxlarge: %s;\n", get_theme_mod( Fonts::HEADING_XXLARGE_FONT_SIZE, Fonts::HEADING_XXLARGE_FONT_SIZE_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-size-heading-xlarge: %s;\n", get_theme_mod( Fonts::HEADING_XLARGE_FONT_SIZE, Fonts::HEADING_XLARGE_FONT_SIZE_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-size-heading-large: %s;\n", get_theme_mod( Fonts::HEADING_LARGE_FONT_SIZE, Fonts::HEADING_LARGE_FONT_SIZE_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-size-heading: %s;\n", get_theme_mod( Fonts::HEADING_FONT_SIZE, Fonts::HEADING_FONT_SIZE_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-size-heading-small: %s;\n", get_theme_mod( Fonts::HEADING_SMALL_FONT_SIZE, Fonts::HEADING_SMALL_FONT_SIZE_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-size-heading-xsmall: %s;\n", get_theme_mod( Fonts::HEADING_XSMALL_FONT_SIZE, Fonts::HEADING_XSMALL_FONT_SIZE_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-size-heading-xxsmall: %s;\n", get_theme_mod( Fonts::HEADING_XXSMALL_FONT_SIZE, Fonts::HEADING_XXSMALL_FONT_SIZE_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-font-size-heading-xxxsmall: %s;\n", get_theme_mod( Fonts::HEADING_XXXSMALL_FONT_SIZE, Fonts::HEADING_XXXSMALL_FONT_SIZE_DEFAULT ) );

		$root_styles .= sprintf( "\t--global-line-height-heading-xxlarge: %s;\n", get_theme_mod( Fonts::HEADING_XXLARGE_LINE_HEIGHT, Fonts::HEADING_XXLARGE_LINE_HEIGHT_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-line-height-heading-xlarge: %s;\n", get_theme_mod( Fonts::HEADING_XLARGE_LINE_HEIGHT, Fonts::HEADING_XLARGE_LINE_HEIGHT_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-line-height-heading-large: %s;\n", get_theme_mod( Fonts::HEADING_LARGE_LINE_HEIGHT, Fonts::HEADING_LARGE_LINE_HEIGHT_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-line-height-heading: %s;\n", get_theme_mod( Fonts::HEADING_LINE_HEIGHT, Fonts::HEADING_LINE_HEIGHT_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-line-height-heading-small: %s;\n", get_theme_mod( Fonts::HEADING_SMALL_LINE_HEIGHT, Fonts::HEADING_SMALL_LINE_HEIGHT_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-line-height-heading-xsmall: %s;\n", get_theme_mod( Fonts::HEADING_XSMALL_LINE_HEIGHT, Fonts::HEADING_XSMALL_LINE_HEIGHT_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-line-height-heading-xxsmall: %s;\n", get_theme_mod( Fonts::HEADING_XXSMALL_LINE_HEIGHT, Fonts::HEADING_XXSMALL_LINE_HEIGHT_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-line-height-heading-xxxsmall: %s;\n", get_theme_mod( Fonts::HEADING_XXXSMALL_LINE_HEIGHT, Fonts::HEADING_XXXSMALL_LINE_HEIGHT_DEFAULT ) );

		$root_styles .= sprintf( "\t--global-bg-color-primary: %s;\n", get_theme_mod( Colors::LIGHT_BACKGROUND_COLOR, Colors::LIGHT_BACKGROUND_COLOR_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-bg-color-secondary: %s;\n", get_theme_mod( Colors::DARK_BACKGROUND_COLOR, Colors::DARK_BACKGROUND_COLOR_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-highlight-bg-color: %s;\n", get_theme_mod( Colors::HIGHLIGHT_BACKGROUND_COLOR, Colors::PRIMARY_FOCUS_COLOR_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-highlight-text-color: %s;\n", get_theme_mod( Colors::HIGHLIGHT_TEXT_COLOR, Colors::SECONDARY_FOCUS_COLOR_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-text-selection-color: %s;\n", get_theme_mod( Colors::TEXT_SELECTION_COLOR, Colors::TEXT_SELECTION_COLOR_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-nav-logo-width: %spx;\n", get_theme_mod( Header::SITE_BRANDING_NAV_LOGO_WIDTH, Header::SITE_BRANDING_NAV_LOGO_WIDTH_DEFAULT ) );
		$root_styles .= sprintf( "\t--global-nav-logo-mobile-width: %spx;\n", get_theme_mod( Header::SITE_BRANDING_NAV_LOGO_MOBILE_WIDTH, Header::SITE_BRANDING_NAV_LOGO_MOBILE_WIDTH_DEFAULT ) );
		$root_styles .= $this->get_button_border_radius();
		$root_styles .= $this->get_button_text_transform();
		$root_styles .= $this->get_media_border_radius();
		$root_styles .= $this->get_primary_font();
		$root_styles .= $this->get_secondary_font();

		echo sprintf( "<style>\n:root {\n%s}\n</style>\n", $root_styles );
	}

	/**
	 * Gets the primary font css var from the customizer settings.
	 *
	 * @return string
	 */
	private function get_primary_font(): string {
		$selected_font = get_theme_mod( Fonts::PRIMARY_FONT, Web_Fonts::POPPINS );
		$selected_obj = Web_Fonts::get_single_google_font( $selected_font );

		return sprintf(
			"\t--global-font-family-primary: '%s', %s;\n",
			$selected_obj['label'],
			$selected_obj['fallback']
		);
	}

	/**
	 * Gets the secondary font css var from the customizer settings.
	 *
	 * @return string
	 */
	private function get_secondary_font(): string {
		$selected_font = get_theme_mod( Fonts::SECONDARY_FONT, Web_Fonts::POPPINS );
		$selected_obj = Web_Fonts::get_single_google_font( $selected_font );

		return sprintf(
			"\t--global-font-family-secondary: '%s', %s;\n",
			$selected_obj['label'],
			$selected_obj['fallback']
		);
	}

	/**
	 * Gets the border radius settings for buttons
	 *
	 * @return string
	 */
	private function get_button_border_radius(): string {
		$selected_radius = get_theme_mod( Site_Identity::BUTTON_STYLE, Site_Identity::BUTTON_STYLE_SQUARE );

		if ( Site_Identity::BUTTON_STYLE_PILL === $selected_radius ) {
			$radius = '50px';
		} elseif ( Site_Identity::BUTTON_STYLE_ROUNDED === $selected_radius ) {
			$radius = '8px';
		} else {
			$radius = '0';
		}

		return sprintf(
			"\t--global-button-border-radius: %s;\n",
			$radius
		);
	}

	/**
	 * Gets the text transform style settings for buttons
	 *
	 * @return string
	 */
	private function get_button_text_transform(): string {
		$selected_text_transform = get_theme_mod( Site_Identity::BUTTON_TEXT_TRANSFORM, Site_Identity::BUTTON_TEXT_STYLE_UPPER_CASE );

		if ( Site_Identity::BUTTON_TEXT_STYLE_TITLE_CASE === $selected_text_transform ) {
			$text_transform = 'capitalize !important';
		} elseif ( Site_Identity::BUTTON_TEXT_STYLE_AS_ENTERED === $selected_text_transform ) {
			$text_transform = 'none !important';
		} else {
			$text_transform = 'uppercase !important';
		}

		return sprintf(
			"\t--global-button-text-transform: %s;\n",
			$text_transform
		);
	}

	/**
	 * Gets the border radius settings for media
	 *
	 * @return string
	 */
	private function get_media_border_radius(): string {
		$selected_radius = get_theme_mod( Site_Identity::MEDIA_STYLE, Site_Identity::MEDIA_STYLE_SQUARE );

		if ( Site_Identity::MEDIA_STYLE_ROUNDED === $selected_radius ) {
			$radius = '8px';
		} else {
			$radius = '0';
		}

		return sprintf(
			"\t--global-media-border-radius: %s;\n",
			$radius
		);
	}
}
