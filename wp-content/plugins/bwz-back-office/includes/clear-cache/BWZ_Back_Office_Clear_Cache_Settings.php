<?php

class BWZ_Back_Office_Clear_Cache_Settings {

	private string $field_group_key;

	private string $option_page;

	public function __construct() {
		$this->field_group_key = 'BWZ_Back_Office_Clear_Cache_Settings';
		$this->option_page     = BWZ_Back_Office::bwz_back_office_get_acf_menu();
	}

	public function init(): void {

		if ( function_exists( 'acf_add_local_field_group' ) ):

			$field_group = [
				'key'      => $this->field_group_key,
				'title'    => 'Clear Cache Settings',
				'location' => [
					[
						[
							'param'    => 'options_page',
							'operator' => '==',
							'value'    => $this->option_page,
						],
					],
				],
			];

			acf_add_local_field_group( $field_group );

			$fields = [
				[
					'parent'  => $this->field_group_key,
					'key'     => $this->field_group_key . 'clear_cache_button',
					'name'    => BWZ_Back_Office::bwz_back_office_get_acf_selector( 'clear_cache_button' ),
					'message' => '<button type="button" class="button button-primary" id="bwz-clear-cache-button">Clear Cache</button><br><small>Clicking on this will not save the settings, click in the Update button to save the settings.</small>',
					'type'    => 'message',
				],
				[
					'parent'   => $this->field_group_key,
					'key'      => $this->field_group_key . 'cache_options',
					'name'     => BWZ_Back_Office::bwz_back_office_get_acf_selector( 'cache_options' ),
					'label'    => 'Cache Options',
					'type'     => 'checkbox',
					'required' => false,
					'choices'  => [
						'pagecache'    => 'Page Cache',
						'hosting'      => 'Hosting',
						'integrations' => 'Integrations',
						'firstloadcss' => 'First Load CSS',
					],
				]
			];

			acf_add_local_fields( $fields );

		endif;
	}
}
