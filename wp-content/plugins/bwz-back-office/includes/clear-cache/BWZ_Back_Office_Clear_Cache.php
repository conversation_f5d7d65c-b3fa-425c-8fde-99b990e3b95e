<?php

/*
 * Clear Cache
 */

use Tribe\Project\Assets\Theme\Styles;

class BWZ_Back_Office_Clear_Cache {

	public function __construct() {
		$this->load_dependencies();

		$settings = new BWZ_Back_Office_Clear_Cache_Settings();
		$settings->init();
	}

	private function load_dependencies(): void {
		require_once BWZ_Back_Office::bwz_back_office_get_plugin_path() . 'includes/clear-cache/BWZ_Back_Office_Clear_Cache_Settings.php';
	}

	public function init(): void {
		if ( is_admin() ) {
			add_action( 'acf/admin_enqueue_scripts', [ $this, 'bwz_back_office_clear_cache_load_js' ] );
			add_action( 'wp_ajax_bwz_back_office_clear_cache_ajax', [
				$this,
				'bwz_back_office_clear_cache_ajax_callback'
			] );
		}
	}

	public function bwz_back_office_clear_cache_load_js(): void {
		BWZ_Back_Office::bwz_back_office_enqueue_custom_script( 'clear-cache', 'clear-cache.js' );
	}

	public function bwz_back_office_purge_first_load_css(): bool {
		return delete_transient( Styles::FIRST_LOAD_TRANSIENT_NAME );
	}

	public function bwz_back_office_clear_cache_ajax_callback() {
		check_ajax_referer( BWZ_Back_Office::bwz_back_office_get_ajax_nonce_action(), 'nonce' );

		try {
			if ( ! defined( 'WPR_PDIR' ) ) {
				throw new Exception( 'WP Raiser not found' );
			}

			$data = $_POST['data'];

			$version      = wpraiser_cache_increment();
			$pagecache    = in_array( 'pagecache', $data['options'] ) ? wpraiser_purge_pagecache() : 'Skipped';
			$hosting      = in_array( 'hosting', $data['options'] ) ? wpraiser_purge_hosting() : 'Skipped';
			$integrations = in_array( 'integrations', $data['options'] ) ? wpraiser_purge_integrations() : 'Skipped';
			$firstloadcss = in_array( 'firstloadcss', $data['options'] ) ? $this->bwz_back_office_purge_first_load_css() : 'Skipped';

			$response = [
				'message' => 'All Cache Purged with WP Raiser',
				'details' => [
					'version'      => $version,
					'pagecache'    => $pagecache,
					'hosting'      => $hosting,
					'integrations' => $integrations,
					'firstloadcss' => $firstloadcss,
				],
			];

			wp_send_json_success( $response );
			wp_die();
		} catch ( Exception $e ) {
			wp_send_json_error( [
				'message' => $e->getMessage(),
			] );
			wp_die();
		}
	}

}
